import request from '@/utils/request'
import { serverBaseUrl } from '@/utils/common'

export function getSettingValue(data: any) {
  return request({
    url: '/setting/settings/getSettingValue',
    method: 'post',
    data,
  })
}

export function statusList(data: any) {
  return request({
    url: '/setting/settings/statusList',
    method: 'post',
    data,
  })
}

export function overView(data: any) {
  return request({
    url: '/setting/settings/overView',
    method: 'post',
    data,
  })
}

export function doEditByName(data: any) {
  return request({
    url: '/setting/settings/doEditByName',
    method: 'post',
    data,
  })
}

export function doNowTask(data: any) {
  return request({
    url: '/setting/doNowTask',
    method: 'post',
    data,
  })
}

/**
 * @description: bi上传文件
 * @return {*}
 */
export const UploadServer = `${serverBaseUrl()}/setting/attachment/upload`

/**
 * @description: 下载文件
 * @return {*}
 */
export const DownloadServer = `${serverBaseUrl()}/setting/attachment/download`

/**
 * @description: bi Fms 上传文件
 * @return {*}
 */
export const FmsUploadServer = `${serverBaseUrl()}/api/fms/files`
