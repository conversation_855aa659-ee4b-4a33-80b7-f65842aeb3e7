/**
 * FMS 目录管理 API
 */

import request from '../request'

const BASE_URL = '/api/fms/directories'

/**
 * 获取目录列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const list = (params = {}) => {
  return request({
    url: BASE_URL,
    method: 'GET',
    params
  })
}

/**
 * 获取目录树
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const tree = (params = {}) => {
  return request({
    url: `${BASE_URL}/tree`,
    method: 'GET',
    params
  })
}

/**
 * 获取单个目录信息
 * @param {number} id 目录ID
 * @returns {Promise}
 */
export const show = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'GET'
  })
}

/**
 * 创建目录
 * @param {Object} data 目录数据
 * @param {number|null} data.parentId 父目录ID
 * @param {string} data.name 目录名称
 * @param {string} data.visibility 可见性
 * @param {number} data.sortOrder 排序
 * @returns {Promise}
 */
export const create = (data) => {
  return request({
    url: BASE_URL,
    method: 'POST',
    data
  })
}

/**
 * 重命名目录
 * @param {number} id 目录ID
 * @param {Object} data 更新数据
 * @param {string} data.name 新名称
 * @returns {Promise}
 */
export const rename = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/rename`,
    method: 'PUT',
    data
  })
}

/**
 * 移动目录
 * @param {number} id 目录ID
 * @param {Object} data 移动数据
 * @param {number|null} data.newParentId 新父目录ID
 * @returns {Promise}
 */
export const move = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/move`,
    method: 'PUT',
    data
  })
}

/**
 * 更新目录可见性
 * @param {number} id 目录ID
 * @param {Object} data 更新数据
 * @param {string} data.visibility 可见性
 * @returns {Promise}
 */
export const updateVisibility = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/visibility`,
    method: 'PUT',
    data
  })
}

/**
 * 删除目录（软删除）
 * @param {number} id 目录ID
 * @returns {Promise}
 */
export const destroy = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'DELETE'
  })
}

/**
 * 恢复目录
 * @param {number} id 目录ID
 * @returns {Promise}
 */
export const restore = (id) => {
  return request({
    url: `${BASE_URL}/${id}/restore`,
    method: 'POST'
  })
}

/**
 * 更新目录排序
 * @param {Array} data 排序数据
 * @returns {Promise}
 */
export const updateSortOrder = (data) => {
  return request({
    url: `${BASE_URL}/sort`,
    method: 'PUT',
    data
  })
}

/**
 * 批量操作目录
 * @param {Object} data 批量操作数据
 * @param {Array} data.directoryIds 目录ID列表
 * @param {string} data.action 操作类型
 * @param {Object} data.params 操作参数
 * @returns {Promise}
 */
export const batchOperation = (data) => {
  return request({
    url: `${BASE_URL}/batch`,
    method: 'POST',
    data
  })
}

/**
 * 获取目录统计信息
 * @param {number} id 目录ID
 * @returns {Promise}
 */
export const getStats = (id) => {
  return request({
    url: `${BASE_URL}/${id}/stats`,
    method: 'GET'
  })
}

/**
 * 检查目录权限
 * @param {number} id 目录ID
 * @param {Object} params 权限检查参数
 * @returns {Promise}
 */
export const checkPermission = (id, params) => {
  return request({
    url: `${BASE_URL}/${id}/permission`,
    method: 'GET',
    params
  })
}

/**
 * 搜索目录
 * @param {Object} params 搜索参数
 * @returns {Promise}
 */
export const search = (params) => {
  return request({
    url: `${BASE_URL}/search`,
    method: 'GET',
    params
  })
}
