/**
 * FMS 标签管理 API
 */

import request from '../request'

const BASE_URL = '/api/fms/tags'

/**
 * 获取标签列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const list = (params = {}) => {
  return request({
    url: BASE_URL,
    method: 'GET',
    params,
  })
}

/**
 * 获取单个标签信息
 * @param {number} id 标签ID
 * @returns {Promise}
 */
export const show = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'GET',
  })
}

/**
 * 创建标签
 * @param {Object} data 标签数据
 * @returns {Promise}
 */
export const create = (data) => {
  return request({
    url: BASE_URL,
    method: 'POST',
    data,
  })
}

/**
 * 更新标签
 * @param {number} id 标签ID
 * @param {Object} data 更新数据
 * @returns {Promise}
 */
export const update = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除标签
 * @param {number} id 标签ID
 * @returns {Promise}
 */
export const destroy = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'DELETE',
  })
}

/**
 * 为文件分配标签
 * @param {Object} data 分配数据
 * @returns {Promise}
 */
export const assign = (data) => {
  return request({
    url: `${BASE_URL}/assign`,
    method: 'POST',
    data,
  })
}

/**
 * 取消文件标签分配
 * @param {Object} data 取消分配数据
 * @returns {Promise}
 */
export const unassign = (data) => {
  return request({
    url: `${BASE_URL}/unassign`,
    method: 'POST',
    data,
  })
}

/**
 * 获取文件的标签列表
 * @param {number} fileId 文件ID
 * @returns {Promise}
 */
export const listByFile = (fileId) => {
  return request({
    url: `/api/fms/files/${fileId}/tags`,
    method: 'GET',
  })
}

/**
 * 批量分配标签
 * @param {Object} data 批量分配数据
 * @returns {Promise}
 */
export const batchAssign = (data) => {
  return request({
    url: `${BASE_URL}/batch-assign`,
    method: 'POST',
    data,
  })
}

/**
 * 批量取消标签分配
 * @param {Object} data 批量取消分配数据
 * @returns {Promise}
 */
export const batchUnassign = (data) => {
  return request({
    url: `${BASE_URL}/batch-unassign`,
    method: 'POST',
    data,
  })
}

/**
 * 获取标签统计信息
 * @param {Object} params 统计参数
 * @returns {Promise}
 */
export const getStats = (params = {}) => {
  return request({
    url: `${BASE_URL}/stats`,
    method: 'GET',
    params,
  })
}

/**
 * 搜索标签
 * @param {Object} params 搜索参数
 * @returns {Promise}
 */
export const search = (params) => {
  return request({
    url: `${BASE_URL}/search`,
    method: 'GET',
    params,
  })
}

/**
 * 获取热门标签
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getPopular = (params = {}) => {
  return request({
    url: `${BASE_URL}/popular`,
    method: 'GET',
    params,
  })
}

/**
 * 合并标签
 * @param {Object} data 合并数据
 * @returns {Promise}
 */
export const merge = (data) => {
  return request({
    url: `${BASE_URL}/merge`,
    method: 'POST',
    data,
  })
}
