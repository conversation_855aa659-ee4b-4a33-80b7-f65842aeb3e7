/**
 * FMS 回收站管理 API
 */

import request from '../request'

const BASE_URL = '/api/fms/recycle-bin'

/**
 * 获取回收站列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const listDeleted = (params = {}) => {
  return request({
    url: BASE_URL,
    method: 'GET',
    params
  })
}

/**
 * 恢复项目
 * @param {number} id 回收站项目ID
 * @returns {Promise}
 */
export const restore = (id) => {
  return request({
    url: `${BASE_URL}/${id}/restore`,
    method: 'POST'
  })
}

/**
 * 彻底删除项目
 * @param {number} id 回收站项目ID
 * @returns {Promise}
 */
export const purge = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'DELETE'
  })
}

/**
 * 批量恢复项目
 * @param {Object} data 批量恢复数据
 * @returns {Promise}
 */
export const batchRestore = (data) => {
  return request({
    url: `${BASE_URL}/batch-restore`,
    method: 'POST',
    data
  })
}

/**
 * 批量彻底删除项目
 * @param {Object} data 批量删除数据
 * @returns {Promise}
 */
export const batchPurge = (data) => {
  return request({
    url: `${BASE_URL}/batch-purge`,
    method: 'POST',
    data
  })
}

/**
 * 清空回收站
 * @param {Object} data 清空数据
 * @returns {Promise}
 */
export const empty = (data = {}) => {
  return request({
    url: `${BASE_URL}/empty`,
    method: 'POST',
    data
  })
}

/**
 * 获取回收站统计信息
 * @param {Object} params 统计参数
 * @returns {Promise}
 */
export const getStats = (params = {}) => {
  return request({
    url: `${BASE_URL}/stats`,
    method: 'GET',
    params
  })
}

/**
 * 搜索回收站项目
 * @param {Object} params 搜索参数
 * @returns {Promise}
 */
export const search = (params) => {
  return request({
    url: `${BASE_URL}/search`,
    method: 'GET',
    params
  })
}

/**
 * 获取项目删除历史
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getDeleteHistory = (params = {}) => {
  return request({
    url: `${BASE_URL}/history`,
    method: 'GET',
    params
  })
}

/**
 * 设置自动清理规则
 * @param {Object} data 规则数据
 * @returns {Promise}
 */
export const setAutoCleanRule = (data) => {
  return request({
    url: `${BASE_URL}/auto-clean`,
    method: 'POST',
    data
  })
}

/**
 * 获取自动清理规则
 * @returns {Promise}
 */
export const getAutoCleanRule = () => {
  return request({
    url: `${BASE_URL}/auto-clean`,
    method: 'GET'
  })
}

/**
 * 预览恢复操作
 * @param {Object} data 预览数据
 * @returns {Promise}
 */
export const previewRestore = (data) => {
  return request({
    url: `${BASE_URL}/preview-restore`,
    method: 'POST',
    data
  })
}
