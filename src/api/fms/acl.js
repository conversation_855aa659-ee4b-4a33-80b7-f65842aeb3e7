/**
 * FMS ACL 权限管理 API
 */

import request from '../request'

const BASE_URL = '/api/fms/acl'

/**
 * 获取ACL规则列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const listRules = (params = {}) => {
  return request({
    url: `${BASE_URL}/rules`,
    method: 'GET',
    params,
  })
}

/**
 * 创建ACL规则
 * @param {Object} data 规则数据
 * @returns {Promise}
 */
export const createRule = (data) => {
  return request({
    url: `${BASE_URL}/rules`,
    method: 'POST',
    data,
  })
}

/**
 * 删除ACL规则
 * @param {number} id 规则ID
 * @returns {Promise}
 */
export const deleteRule = (id) => {
  return request({
    url: `${BASE_URL}/rules/${id}`,
    method: 'DELETE',
  })
}

/**
 * 添加规则条件
 * @param {number} ruleId 规则ID
 * @param {Object} data 条件数据
 * @returns {Promise}
 */
export const addCondition = (ruleId, data) => {
  return request({
    url: `${BASE_URL}/rules/${ruleId}/conditions`,
    method: 'POST',
    data,
  })
}

/**
 * 移除规则条件
 * @param {number} conditionId 条件ID
 * @returns {Promise}
 */
export const removeCondition = (conditionId) => {
  return request({
    url: `${BASE_URL}/conditions/${conditionId}`,
    method: 'DELETE',
  })
}

/**
 * 检查权限
 * @param {Object} data 权限检查数据
 * @returns {Promise}
 */
export const check = (data) => {
  return request({
    url: `${BASE_URL}/check`,
    method: 'POST',
    data,
  })
}

/**
 * 计算有效权限
 * @param {Object} data 权限计算数据
 * @returns {Promise}
 */
export const compute = (data) => {
  return request({
    url: `${BASE_URL}/compute`,
    method: 'POST',
    data,
  })
}

/**
 * 获取主体列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getSubjects = (params = {}) => {
  return request({
    url: `${BASE_URL}/subjects`,
    method: 'GET',
    params,
  })
}

/**
 * 获取单个主体信息
 * @param {number} id 主体ID
 * @returns {Promise}
 */
export const getSubject = (id) => {
  return request({
    url: `${BASE_URL}/subjects/${id}`,
    method: 'GET',
  })
}

/**
 * 获取用户的主体信息
 * @param {number} userId 用户ID
 * @returns {Promise}
 */
export const getSubjectsForUser = (userId) => {
  return request({
    url: `${BASE_URL}/users/${userId}/subjects`,
    method: 'GET',
  })
}

/**
 * 批量创建ACL规则
 * @param {Object} data 批量规则数据
 * @returns {Promise}
 */
export const batchCreateRules = (data) => {
  return request({
    url: `${BASE_URL}/rules/batch`,
    method: 'POST',
    data,
  })
}

/**
 * 批量删除ACL规则
 * @param {Object} data 批量删除数据
 * @returns {Promise}
 */
export const batchDeleteRules = (data) => {
  return request({
    url: `${BASE_URL}/rules/batch`,
    method: 'DELETE',
    data,
  })
}

/**
 * 获取权限模板列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getTemplates = (params = {}) => {
  return request({
    url: `${BASE_URL}/templates`,
    method: 'GET',
    params,
  })
}

/**
 * 创建权限模板
 * @param {Object} data 模板数据
 * @returns {Promise}
 */
export const createTemplate = (data) => {
  return request({
    url: `${BASE_URL}/templates`,
    method: 'POST',
    data,
  })
}

/**
 * 应用权限模板
 * @param {number} templateId 模板ID
 * @param {Object} data 应用数据
 * @returns {Promise}
 */
export const applyTemplate = (templateId, data) => {
  return request({
    url: `${BASE_URL}/templates/${templateId}/apply`,
    method: 'POST',
    data,
  })
}

/**
 * 获取权限继承信息
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getInheritance = (params) => {
  return request({
    url: `${BASE_URL}/inheritance`,
    method: 'GET',
    params,
  })
}

/**
 * 获取权限变更历史
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getHistory = (params = {}) => {
  return request({
    url: `${BASE_URL}/history`,
    method: 'GET',
    params,
  })
}

/**
 * 获取权限统计信息
 * @param {Object} params 统计参数
 * @returns {Promise}
 */
export const getStats = (params = {}) => {
  return request({
    url: `${BASE_URL}/stats`,
    method: 'GET',
    params,
  })
}

/**
 * 验证条件表达式
 * @param {Object} data 条件数据
 * @returns {Promise}
 */
export const validateCondition = (data) => {
  return request({
    url: `${BASE_URL}/conditions/validate`,
    method: 'POST',
    data,
  })
}

/**
 * 测试权限规则
 * @param {Object} data 测试数据
 * @returns {Promise}
 */
export const testRules = (data) => {
  return request({
    url: `${BASE_URL}/test`,
    method: 'POST',
    data,
  })
}

/**
 * 导出权限配置
 * @param {Object} params 导出参数
 * @returns {Promise}
 */
export const exportConfig = (params = {}) => {
  return request({
    url: `${BASE_URL}/export`,
    method: 'GET',
    params,
    responseType: 'blob',
  })
}

/**
 * 导入权限配置
 * @param {Object} data 导入数据
 * @returns {Promise}
 */
export const importConfig = (data) => {
  const formData = new FormData()
  formData.append('file', data.file)
  if (data.overwrite !== undefined) formData.append('overwrite', data.overwrite)

  return request({
    url: `${BASE_URL}/import`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
