/**
 * FMS 文件管理 API
 */

import request from '@/utils/request'

const BASE_URL = '/api/fms/files'

/**
 * 获取文件列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const list = (params = {}) => {
  return request({
    url: BASE_URL,
    method: 'GET',
    params,
  })
}

/**
 * 获取单个文件信息
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const show = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'GET',
  })
}

/**
 * 上传文件
 * @param {Object} data 上传数据
 * @param {Function} onProgress 进度回调
 * @returns {Promise}
 */
export const upload = (data, onProgress) => {
  const formData = new FormData()

  // 添加文件
  formData.append('file', data.file)

  // 添加其他参数
  if (data.directoryId) formData.append('directory_id', data.directoryId)
  if (data.name) formData.append('name', data.name)
  if (data.visibility) formData.append('visibility', data.visibility)
  if (data.tags) formData.append('tags', JSON.stringify(data.tags))
  if (data.metadata) formData.append('metadata', JSON.stringify(data.metadata))
  if (data.overwrite !== undefined) formData.append('overwrite', data.overwrite)
  if (data.createVersion !== undefined)
    formData.append('create_version', data.createVersion)
  if (data.comment) formData.append('comment', data.comment)

  return request({
    url: BASE_URL,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress) {
        const percent = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress({
          loaded: progressEvent.loaded,
          total: progressEvent.total,
          percent: percent,
        })
      }
    },
  })
}

/**
 * 下载文件
 * @param {number} id 文件ID
 * @param {number} version 版本号（可选）
 * @returns {Promise}
 */
export const download = (id, version) => {
  const params = {}
  if (version) params.version = version

  return request({
    url: `${BASE_URL}/${id}/download`,
    method: 'GET',
    params,
    responseType: 'blob',
  }).then((response) => {
    // 从响应头获取文件名
    const contentDisposition = response.headers['content-disposition']
    let filename = `file_${id}`

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(
        /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      )
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }

    // 创建下载URL
    const url = window.URL.createObjectURL(new Blob([response.data]))

    return {
      url,
      filename,
      size: response.data.size,
      mimeType: response.headers['content-type'],
    }
  })
}

/**
 * 更新文件信息
 * @param {number} id 文件ID
 * @param {Object} data 更新数据
 * @returns {Promise}
 */
export const update = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除文件（软删除）
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const destroy = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'DELETE',
  })
}

/**
 * 恢复文件
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const restore = (id) => {
  return request({
    url: `${BASE_URL}/${id}/restore`,
    method: 'POST',
  })
}

/**
 * 获取文件版本列表
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const versions = (id) => {
  return request({
    url: `${BASE_URL}/${id}/versions`,
    method: 'GET',
  })
}

/**
 * 回滚文件版本
 * @param {number} id 文件ID
 * @param {number} version 版本号
 * @param {Object} data 回滚数据
 * @returns {Promise}
 */
export const revertVersion = (id, version, data = {}) => {
  return request({
    url: `${BASE_URL}/${id}/versions/${version}/revert`,
    method: 'POST',
    data,
  })
}

/**
 * 更新文件可见性
 * @param {number} id 文件ID
 * @param {Object} data 更新数据
 * @returns {Promise}
 */
export const updateVisibility = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/visibility`,
    method: 'PUT',
    data,
  })
}

/**
 * 获取文件预览信息
 * @param {number} id 文件ID
 * @returns {Promise}
 */
export const getPreviewInfo = (id) => {
  return request({
    url: `${BASE_URL}/${id}/preview`,
    method: 'GET',
  })
}

/**
 * 获取文件缩略图
 * @param {number} id 文件ID
 * @param {Object} params 缩略图参数
 * @returns {Promise}
 */
export const getThumbnail = (id, params = {}) => {
  return request({
    url: `${BASE_URL}/${id}/thumbnail`,
    method: 'GET',
    params,
    responseType: 'blob',
  })
}

/**
 * 批量操作文件
 * @param {Object} data 批量操作数据
 * @returns {Promise}
 */
export const batchOperation = (data) => {
  return request({
    url: `${BASE_URL}/batch`,
    method: 'POST',
    data,
  })
}

/**
 * 搜索文件
 * @param {Object} params 搜索参数
 * @returns {Promise}
 */
export const search = (params) => {
  return request({
    url: `${BASE_URL}/search`,
    method: 'GET',
    params,
  })
}

/**
 * 获取文件统计信息
 * @param {Object} params 统计参数
 * @returns {Promise}
 */
export const getStats = (params = {}) => {
  return request({
    url: `${BASE_URL}/stats`,
    method: 'GET',
    params,
  })
}

/**
 * 检查文件权限
 * @param {number} id 文件ID
 * @param {Object} params 权限检查参数
 * @returns {Promise}
 */
export const checkPermission = (id, params) => {
  return request({
    url: `${BASE_URL}/${id}/permission`,
    method: 'GET',
    params,
  })
}

/**
 * 移动文件到目录
 * @param {number} id 文件ID
 * @param {Object} data 移动数据
 * @returns {Promise}
 */
export const move = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/move`,
    method: 'PUT',
    data,
  })
}

/**
 * 复制文件
 * @param {number} id 文件ID
 * @param {Object} data 复制数据
 * @returns {Promise}
 */
export const copy = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/copy`,
    method: 'POST',
    data,
  })
}
