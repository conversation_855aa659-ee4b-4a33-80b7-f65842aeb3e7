/**
 * FMS 操作日志 API
 */

import request from '../request'

const BASE_URL = '/api/fms/logs'

/**
 * 获取操作日志列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const list = (params = {}) => {
  return request({
    url: BASE_URL,
    method: 'GET',
    params
  })
}

/**
 * 获取单个操作日志详情
 * @param {number} id 日志ID
 * @returns {Promise}
 */
export const show = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'GET'
  })
}

/**
 * 搜索操作日志
 * @param {Object} params 搜索参数
 * @returns {Promise}
 */
export const search = (params) => {
  return request({
    url: `${BASE_URL}/search`,
    method: 'GET',
    params
  })
}

/**
 * 获取日志统计信息
 * @param {Object} params 统计参数
 * @returns {Promise}
 */
export const getStats = (params = {}) => {
  return request({
    url: `${BASE_URL}/stats`,
    method: 'GET',
    params
  })
}

/**
 * 获取用户操作日志
 * @param {number} userId 用户ID
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getUserLogs = (userId, params = {}) => {
  return request({
    url: `${BASE_URL}/users/${userId}`,
    method: 'GET',
    params
  })
}

/**
 * 获取目标对象的操作日志
 * @param {string} targetType 目标类型
 * @param {number} targetId 目标ID
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getTargetLogs = (targetType, targetId, params = {}) => {
  return request({
    url: `${BASE_URL}/targets/${targetType}/${targetId}`,
    method: 'GET',
    params
  })
}

/**
 * 导出操作日志
 * @param {Object} params 导出参数
 * @returns {Promise}
 */
export const exportLogs = (params = {}) => {
  return request({
    url: `${BASE_URL}/export`,
    method: 'GET',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取操作类型列表
 * @returns {Promise}
 */
export const getActionTypes = () => {
  return request({
    url: `${BASE_URL}/action-types`,
    method: 'GET'
  })
}

/**
 * 获取日志趋势数据
 * @param {Object} params 趋势参数
 * @returns {Promise}
 */
export const getTrends = (params = {}) => {
  return request({
    url: `${BASE_URL}/trends`,
    method: 'GET',
    params
  })
}

/**
 * 获取热门操作
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getPopularActions = (params = {}) => {
  return request({
    url: `${BASE_URL}/popular-actions`,
    method: 'GET',
    params
  })
}

/**
 * 获取活跃用户
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getActiveUsers = (params = {}) => {
  return request({
    url: `${BASE_URL}/active-users`,
    method: 'GET',
    params
  })
}

/**
 * 清理过期日志
 * @param {Object} data 清理数据
 * @returns {Promise}
 */
export const cleanExpiredLogs = (data = {}) => {
  return request({
    url: `${BASE_URL}/clean`,
    method: 'POST',
    data
  })
}

/**
 * 获取日志保留策略
 * @returns {Promise}
 */
export const getRetentionPolicy = () => {
  return request({
    url: `${BASE_URL}/retention-policy`,
    method: 'GET'
  })
}

/**
 * 设置日志保留策略
 * @param {Object} data 策略数据
 * @returns {Promise}
 */
export const setRetentionPolicy = (data) => {
  return request({
    url: `${BASE_URL}/retention-policy`,
    method: 'POST',
    data
  })
}
