/**
 * FMS 分享管理 API
 */

import request from '../request'

const BASE_URL = '/api/fms/shares'

/**
 * 创建分享
 * @param {Object} data 分享数据
 * @returns {Promise}
 */
export const create = (data) => {
  return request({
    url: BASE_URL,
    method: 'POST',
    data,
  })
}

/**
 * 撤销分享
 * @param {number} id 分享ID
 * @returns {Promise}
 */
export const revoke = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'DELETE',
  })
}

/**
 * 通过Token获取分享信息
 * @param {string} token 分享Token
 * @returns {Promise}
 */
export const showByToken = (token) => {
  return request({
    url: `${BASE_URL}/${token}`,
    method: 'GET',
  })
}

/**
 * 验证分享密码
 * @param {string} token 分享Token
 * @param {Object} data 验证数据
 * @returns {Promise}
 */
export const validate = (token, data) => {
  return request({
    url: `/api/public/shares/${token}/verify`,
    method: 'POST',
    data,
  })
}

/**
 * 通过Token下载文件
 * @param {string} token 分享Token
 * @param {Object} params 下载参数
 * @returns {Promise}
 */
export const downloadByToken = (token, params = {}) => {
  return request({
    url: `/api/public/shares/${token}/download`,
    method: 'GET',
    params,
    responseType: 'blob',
  }).then((response) => {
    // 从响应头获取文件名
    const contentDisposition = response.headers['content-disposition']
    let filename = 'download'

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(
        /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      )
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }

    // 创建下载URL
    const url = window.URL.createObjectURL(new Blob([response.data]))

    return {
      url,
      filename,
      size: response.data.size,
      mimeType: response.headers['content-type'],
    }
  })
}

/**
 * 获取分享列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const list = (params = {}) => {
  return request({
    url: BASE_URL,
    method: 'GET',
    params,
  })
}

/**
 * 获取单个分享信息
 * @param {number} id 分享ID
 * @returns {Promise}
 */
export const show = (id) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'GET',
  })
}

/**
 * 更新分享设置
 * @param {number} id 分享ID
 * @param {Object} data 更新数据
 * @returns {Promise}
 */
export const update = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 获取分享访问记录
 * @param {number} id 分享ID
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getVisitLogs = (id, params = {}) => {
  return request({
    url: `${BASE_URL}/${id}/visits`,
    method: 'GET',
    params,
  })
}

/**
 * 记录分享访问
 * @param {string} token 分享Token
 * @param {Object} data 访问数据
 * @returns {Promise}
 */
export const recordVisit = (token, data = {}) => {
  return request({
    url: `/api/public/shares/${token}/visit`,
    method: 'POST',
    data,
  })
}

/**
 * 批量撤销分享
 * @param {Object} data 批量撤销数据
 * @returns {Promise}
 */
export const batchRevoke = (data) => {
  return request({
    url: `${BASE_URL}/batch-revoke`,
    method: 'POST',
    data,
  })
}

/**
 * 获取分享统计信息
 * @param {Object} params 统计参数
 * @returns {Promise}
 */
export const getStats = (params = {}) => {
  return request({
    url: `${BASE_URL}/stats`,
    method: 'GET',
    params,
  })
}

/**
 * 检查分享状态
 * @param {string} token 分享Token
 * @returns {Promise}
 */
export const checkStatus = (token) => {
  return request({
    url: `/api/public/shares/${token}/status`,
    method: 'GET',
  })
}

/**
 * 延长分享有效期
 * @param {number} id 分享ID
 * @param {Object} data 延长数据
 * @returns {Promise}
 */
export const extend = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/extend`,
    method: 'POST',
    data,
  })
}

/**
 * 重置分享密码
 * @param {number} id 分享ID
 * @param {Object} data 重置数据
 * @returns {Promise}
 */
export const resetPassword = (id, data) => {
  return request({
    url: `${BASE_URL}/${id}/reset-password`,
    method: 'POST',
    data,
  })
}

/**
 * 获取分享二维码
 * @param {number} id 分享ID
 * @param {Object} params 二维码参数
 * @returns {Promise}
 */
export const getQRCode = (id, params = {}) => {
  return request({
    url: `${BASE_URL}/${id}/qrcode`,
    method: 'GET',
    params,
    responseType: 'blob',
  })
}
