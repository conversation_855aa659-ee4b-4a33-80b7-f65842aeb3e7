import { defineStore } from 'pinia'
import * as tagApi from '@/api/fms/tags'

export const useTagStore = defineStore('fmsTag', {
  state: () => ({
    // 标签列表
    tags: [],

    // 加载状态
    isLoading: false,
    isCreating: false,
    isUpdating: false,
    isDeleting: false,

    // 分页信息
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0,
    },

    // 筛选条件
    filters: {
      keyword: '',
      category: '',
      color: '',
      status: 'active',
    },

    // 缓存
    tagMap: new Map(),
    lastFetchTime: null,
    cacheExpiry: 5 * 60 * 1000, // 5分钟缓存
  }),

  getters: {
    // 获取标签总数
    totalTags: (state) => state.tags.length,

    // 获取活跃标签
    activeTags: (state) => state.tags.filter((tag) => tag.status === 'active'),

    // 按分类分组的标签
    tagsByCategory: (state) => {
      const groups = {}
      state.tags.forEach((tag) => {
        const category = tag.category || 'default'
        if (!groups[category]) {
          groups[category] = []
        }
        groups[category].push(tag)
      })
      return groups
    },

    // 热门标签（按使用次数排序）
    popularTags: (state) => {
      return [...state.tags]
        .filter((tag) => tag.status === 'active')
        .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
        .slice(0, 20)
    },

    // 最近使用的标签
    recentTags: (state) => {
      return [...state.tags]
        .filter((tag) => tag.status === 'active' && tag.lastUsedAt)
        .sort((a, b) => new Date(b.lastUsedAt) - new Date(a.lastUsedAt))
        .slice(0, 10)
    },

    // 检查缓存是否有效
    isCacheValid: (state) => {
      if (!state.lastFetchTime) return false
      return Date.now() - state.lastFetchTime < state.cacheExpiry
    },
  },

  actions: {
    // 获取标签列表
    async fetchTags(params = {}) {
      // 如果缓存有效且没有新的筛选条件，直接返回缓存
      if (
        this.isCacheValid &&
        !params.force &&
        Object.keys(params).length === 0
      ) {
        return {
          data: this.tags,
          pagination: this.pagination,
        }
      }

      try {
        this.isLoading = true

        const queryParams = {
          page: params.page || this.pagination.current,
          pageSize: params.pageSize || this.pagination.pageSize,
          ...this.filters,
          ...params,
        }

        const response = await tagApi.getTags(queryParams)

        this.tags = response.data || []
        this.pagination = {
          current: response.pagination?.current || 1,
          pageSize: response.pagination?.pageSize || 20,
          total: response.pagination?.total || 0,
        }

        // 更新缓存
        this.updateTagMap()
        this.lastFetchTime = Date.now()

        return response
      } catch (error) {
        console.error('获取标签列表失败:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // 创建标签
    async createTag(tagData) {
      try {
        this.isCreating = true

        const response = await tagApi.createTag(tagData)
        const newTag = response.data

        // 添加到列表
        this.tags.unshift(newTag)
        this.pagination.total++

        // 更新缓存
        this.tagMap.set(newTag.id, newTag)

        return newTag
      } catch (error) {
        console.error('创建标签失败:', error)
        throw error
      } finally {
        this.isCreating = false
      }
    },

    // 更新标签
    async updateTag(id, tagData) {
      try {
        this.isUpdating = true

        const response = await tagApi.updateTag(id, tagData)
        const updatedTag = response.data

        // 更新列表中的标签
        const index = this.tags.findIndex((tag) => tag.id === id)
        if (index !== -1) {
          this.tags[index] = updatedTag
        }

        // 更新缓存
        this.tagMap.set(id, updatedTag)

        return updatedTag
      } catch (error) {
        console.error('更新标签失败:', error)
        throw error
      } finally {
        this.isUpdating = false
      }
    },

    // 删除标签
    async deleteTag(id) {
      try {
        this.isDeleting = true

        await tagApi.deleteTag(id)

        // 从列表中移除
        const index = this.tags.findIndex((tag) => tag.id === id)
        if (index !== -1) {
          this.tags.splice(index, 1)
          this.pagination.total--
        }

        // 从缓存中移除
        this.tagMap.delete(id)
      } catch (error) {
        console.error('删除标签失败:', error)
        throw error
      } finally {
        this.isDeleting = false
      }
    },

    // 批量删除标签
    async batchDeleteTags(ids) {
      try {
        this.isDeleting = true

        await tagApi.batchDeleteTags(ids)

        // 从列表中移除
        ids.forEach((id) => {
          const index = this.tags.findIndex((tag) => tag.id === id)
          if (index !== -1) {
            this.tags.splice(index, 1)
            this.pagination.total--
          }
          this.tagMap.delete(id)
        })
      } catch (error) {
        console.error('批量删除标签失败:', error)
        throw error
      } finally {
        this.isDeleting = false
      }
    },

    // 获取标签详情
    async fetchTagDetail(id) {
      try {
        // 先从缓存获取
        if (this.tagMap.has(id)) {
          return this.tagMap.get(id)
        }

        const response = await tagApi.getTag(id)
        const tag = response.data

        // 更新缓存
        this.tagMap.set(id, tag)

        return tag
      } catch (error) {
        console.error('获取标签详情失败:', error)
        throw error
      }
    },

    // 获取标签统计
    async fetchTagStats() {
      try {
        const response = await tagApi.getTagStats()
        return response.data
      } catch (error) {
        console.error('获取标签统计失败:', error)
        throw error
      }
    },

    // 搜索标签
    async searchTags(keyword) {
      try {
        const response = await tagApi.searchTags({ keyword })
        return response.data || []
      } catch (error) {
        console.error('搜索标签失败:', error)
        throw error
      }
    },

    // 获取标签建议
    async getTagSuggestions(context = {}) {
      try {
        const response = await tagApi.getTagSuggestions(context)
        return response.data || []
      } catch (error) {
        console.error('获取标签建议失败:', error)
        throw error
      }
    },

    // 更新筛选条件
    updateFilters(newFilters) {
      this.filters = { ...this.filters, ...newFilters }
    },

    // 重置筛选条件
    resetFilters() {
      this.filters = {
        keyword: '',
        category: '',
        color: '',
        status: 'active',
      }
    },

    // 更新标签缓存映射
    updateTagMap() {
      this.tagMap.clear()
      this.tags.forEach((tag) => {
        this.tagMap.set(tag.id, tag)
      })
    },

    // 清除缓存
    clearCache() {
      this.tagMap.clear()
      this.lastFetchTime = null
    },

    // 重置状态
    resetState() {
      this.tags = []
      this.pagination = {
        current: 1,
        pageSize: 20,
        total: 0,
      }
      this.resetFilters()
      this.clearCache()
    },
  },
})
