/**
 * FMS 权限状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as aclApi from '../../../api/fms/acl'

export const usePermissionStore = defineStore('fmsPermission', () => {
  // 权限位常量
  const PERMISSION_BITS = {
    VIEW: 1,
    UPLOAD: 2,
    DELETE: 4,
    SHARE: 8,
    MANAGE: 16,
    ADMIN: 32
  }
  
  // 状态
  const currentUserPermissions = ref({})
  const targetPermissions = ref({})
  const aclRules = ref([])
  const subjects = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  
  // 缓存
  const permissionCache = ref(new Map())
  const subjectCache = ref(new Map())
  
  // 计算属性
  const hasAnyPermission = computed(() => {
    return (permissions, bits) => {
      if (!permissions || !bits) return false
      return bits.some(bit => (permissions & bit) === bit)
    }
  })
  
  const hasAllPermissions = computed(() => {
    return (permissions, bits) => {
      if (!permissions || !bits) return false
      return bits.every(bit => (permissions & bit) === bit)
    }
  })
  
  const canView = computed(() => {
    return (targetType, targetId) => {
      const key = `${targetType}:${targetId}`
      const permissions = targetPermissions.value[key]
      return (permissions & PERMISSION_BITS.VIEW) === PERMISSION_BITS.VIEW
    }
  })
  
  const canUpload = computed(() => {
    return (targetType, targetId) => {
      const key = `${targetType}:${targetId}`
      const permissions = targetPermissions.value[key]
      return (permissions & PERMISSION_BITS.UPLOAD) === PERMISSION_BITS.UPLOAD
    }
  })
  
  const canDelete = computed(() => {
    return (targetType, targetId) => {
      const key = `${targetType}:${targetId}`
      const permissions = targetPermissions.value[key]
      return (permissions & PERMISSION_BITS.DELETE) === PERMISSION_BITS.DELETE
    }
  })
  
  const canShare = computed(() => {
    return (targetType, targetId) => {
      const key = `${targetType}:${targetId}`
      const permissions = targetPermissions.value[key]
      return (permissions & PERMISSION_BITS.SHARE) === PERMISSION_BITS.SHARE
    }
  })
  
  const canManage = computed(() => {
    return (targetType, targetId) => {
      const key = `${targetType}:${targetId}`
      const permissions = targetPermissions.value[key]
      return (permissions & PERMISSION_BITS.MANAGE) === PERMISSION_BITS.MANAGE
    }
  })
  
  const isAdmin = computed(() => {
    return (targetType, targetId) => {
      const key = `${targetType}:${targetId}`
      const permissions = targetPermissions.value[key]
      return (permissions & PERMISSION_BITS.ADMIN) === PERMISSION_BITS.ADMIN
    }
  })
  
  // 方法
  const checkPermission = async (userId, targetType, targetId, permissionBit) => {
    try {
      const cacheKey = `${userId}:${targetType}:${targetId}:${permissionBit}`
      
      // 先从缓存获取
      if (permissionCache.value.has(cacheKey)) {
        return permissionCache.value.get(cacheKey)
      }
      
      const response = await aclApi.check({
        userId,
        targetType,
        targetId,
        permissionBit
      })
      
      // 缓存结果
      permissionCache.value.set(cacheKey, response)
      
      return response
      
    } catch (err) {
      error.value = err.message || '权限检查失败'
      throw err
    }
  }
  
  const computeEffectivePermissions = async (userId, targetType, targetId) => {
    try {
      const cacheKey = `${userId}:${targetType}:${targetId}:effective`
      
      // 先从缓存获取
      if (permissionCache.value.has(cacheKey)) {
        return permissionCache.value.get(cacheKey)
      }
      
      const response = await aclApi.compute({
        userId,
        targetType,
        targetId,
        includeInherited: true
      })
      
      // 更新目标权限
      const key = `${targetType}:${targetId}`
      targetPermissions.value[key] = response.effectivePermissions
      
      // 缓存结果
      permissionCache.value.set(cacheKey, response)
      
      return response
      
    } catch (err) {
      error.value = err.message || '计算有效权限失败'
      throw err
    }
  }
  
  const fetchAclRules = async (params = {}) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await aclApi.listRules(params)
      aclRules.value = response.data
      
      return response
      
    } catch (err) {
      error.value = err.message || '获取ACL规则失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  const createAclRule = async (data) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await aclApi.createRule(data)
      const newRule = response.rule
      
      // 添加到规则列表
      aclRules.value.unshift(newRule)
      
      // 清除相关权限缓存
      clearPermissionCache(data.targetType, data.targetId)
      
      return newRule
      
    } catch (err) {
      error.value = err.message || '创建ACL规则失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  const deleteAclRule = async (ruleId) => {
    try {
      isLoading.value = true
      error.value = null
      
      await aclApi.deleteRule(ruleId)
      
      // 从规则列表中移除
      const index = aclRules.value.findIndex(rule => rule.id === ruleId)
      if (index !== -1) {
        const rule = aclRules.value[index]
        aclRules.value.splice(index, 1)
        
        // 清除相关权限缓存
        clearPermissionCache(rule.targetType, rule.targetId)
      }
      
      return true
      
    } catch (err) {
      error.value = err.message || '删除ACL规则失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  const addCondition = async (ruleId, data) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await aclApi.addCondition(ruleId, data)
      const newCondition = response.condition
      
      // 更新规则的条件列表
      const rule = aclRules.value.find(r => r.id === ruleId)
      if (rule) {
        if (!rule.conditions) rule.conditions = []
        rule.conditions.push(newCondition)
        
        // 清除相关权限缓存
        clearPermissionCache(rule.targetType, rule.targetId)
      }
      
      return newCondition
      
    } catch (err) {
      error.value = err.message || '添加条件失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  const removeCondition = async (conditionId) => {
    try {
      isLoading.value = true
      error.value = null
      
      await aclApi.removeCondition(conditionId)
      
      // 从规则的条件列表中移除
      aclRules.value.forEach(rule => {
        if (rule.conditions) {
          const index = rule.conditions.findIndex(c => c.id === conditionId)
          if (index !== -1) {
            rule.conditions.splice(index, 1)
            
            // 清除相关权限缓存
            clearPermissionCache(rule.targetType, rule.targetId)
          }
        }
      })
      
      return true
      
    } catch (err) {
      error.value = err.message || '移除条件失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  const fetchSubjects = async (params = {}) => {
    try {
      const response = await aclApi.getSubjects(params)
      subjects.value = response.data
      
      // 更新主体缓存
      response.data.forEach(subject => {
        subjectCache.value.set(subject.id, subject)
      })
      
      return response
      
    } catch (err) {
      error.value = err.message || '获取主体列表失败'
      throw err
    }
  }
  
  const getSubject = async (id) => {
    // 先从缓存获取
    if (subjectCache.value.has(id)) {
      return subjectCache.value.get(id)
    }
    
    try {
      const subject = await aclApi.getSubject(id)
      subjectCache.value.set(id, subject)
      return subject
    } catch (err) {
      error.value = err.message || '获取主体信息失败'
      throw err
    }
  }
  
  const clearPermissionCache = (targetType, targetId) => {
    const keysToDelete = []
    
    for (const key of permissionCache.value.keys()) {
      if (key.includes(`${targetType}:${targetId}`)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => {
      permissionCache.value.delete(key)
    })
    
    // 清除目标权限
    const targetKey = `${targetType}:${targetId}`
    delete targetPermissions.value[targetKey]
  }
  
  const clearAllCache = () => {
    permissionCache.value.clear()
    subjectCache.value.clear()
    targetPermissions.value = {}
  }
  
  const setLoading = (loading) => {
    isLoading.value = loading
  }
  
  const setError = (err) => {
    error.value = err
  }
  
  const clearError = () => {
    error.value = null
  }
  
  // 工具方法
  const getPermissionLabels = (permissionBits) => {
    const labels = []
    
    if (permissionBits & PERMISSION_BITS.VIEW) labels.push('查看')
    if (permissionBits & PERMISSION_BITS.UPLOAD) labels.push('上传')
    if (permissionBits & PERMISSION_BITS.DELETE) labels.push('删除')
    if (permissionBits & PERMISSION_BITS.SHARE) labels.push('分享')
    if (permissionBits & PERMISSION_BITS.MANAGE) labels.push('管理')
    if (permissionBits & PERMISSION_BITS.ADMIN) labels.push('管理员')
    
    return labels
  }
  
  const hasPermission = (permissions, bit) => {
    return (permissions & bit) === bit
  }
  
  const addPermission = (permissions, bit) => {
    return permissions | bit
  }
  
  const removePermission = (permissions, bit) => {
    return permissions & ~bit
  }
  
  const togglePermission = (permissions, bit) => {
    return permissions ^ bit
  }
  
  return {
    // 常量
    PERMISSION_BITS,
    
    // 状态
    currentUserPermissions,
    targetPermissions,
    aclRules,
    subjects,
    isLoading,
    error,
    
    // 计算属性
    hasAnyPermission,
    hasAllPermissions,
    canView,
    canUpload,
    canDelete,
    canShare,
    canManage,
    isAdmin,
    
    // 方法
    checkPermission,
    computeEffectivePermissions,
    fetchAclRules,
    createAclRule,
    deleteAclRule,
    addCondition,
    removeCondition,
    fetchSubjects,
    getSubject,
    clearPermissionCache,
    clearAllCache,
    setLoading,
    setError,
    clearError,
    
    // 工具方法
    getPermissionLabels,
    hasPermission,
    addPermission,
    removePermission,
    togglePermission
  }
})
