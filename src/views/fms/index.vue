<template>
  <div class="fms-main">
    <el-container class="fms-main__container">
      <el-header class="fms-main__header">
        <Breadcrumb :items="breadcrumbItems" @change="onBreadcrumbChange" />
        <div class="fms-main__toolbar">
          <SearchBar 
            v-model="searchKeyword" 
            placeholder="搜索文件和目录"
            @search="onSearch"
          />
        </div>
      </el-header>
      
      <el-container class="fms-main__body">
        <el-aside class="fms-main__aside" width="280px">
          <DirectoryTree 
            :selected-id="currentDirectoryId"
            :height="treeHeight"
            @select="onDirectorySelect"
          />
        </el-aside>
        
        <el-main class="fms-main__main">
          <!-- 查询筛选区 -->
          <FilterPanel 
            v-model="filterParams"
            :fields="filterFields"
            @submit="onFilterSubmit"
            @reset="onFilterReset"
          />
          
          <!-- 目录列表 -->
          <DirectoryList 
            v-if="showDirectoryList"
            :parent-id="currentDirectoryId"
            :filters="filterParams"
            @refresh="refreshDirectoryList"
            @rename="onDirectoryRename"
            @move="onDirectoryMove"
          />
          
          <!-- 文件列表 -->
          <FileList 
            v-if="showFileList"
            :directory-id="currentDirectoryId"
            :filters="filterParams"
            @refresh="refreshFileList"
            @preview="onFilePreview"
            @delete="onFileDelete"
          />
          
          <!-- 分页 -->
          <Pagination 
            :total="pagination.total"
            :page="pagination.page"
            :page-size="pagination.pageSize"
            @change="onPaginationChange"
          />
        </el-main>
      </el-container>
    </el-container>
    
    <!-- 对话框组件 -->
    <CreateDirectoryDialog 
      v-model:visible="dialogs.createDirectory"
      :parent-id="currentDirectoryId"
      @success="onDirectoryCreated"
    />
    
    <RenameDirectoryDialog 
      v-model:visible="dialogs.renameDirectory"
      :id="selectedDirectory?.id"
      :name="selectedDirectory?.name"
      @success="onDirectoryRenamed"
    />
    
    <MoveDirectoryDialog 
      v-model:visible="dialogs.moveDirectory"
      :id="selectedDirectory?.id"
      :target-parent-id="selectedDirectory?.parentId"
      @success="onDirectoryMoved"
    />
    
    <FilePreview 
      v-if="previewFile"
      :file-id="previewFile.id"
      @close="closeFilePreview"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, inject } from 'vue'
import { useFmsStore } from './stores/fmsStore'
import { useDirectoryStore } from './stores/directoryStore'
import { useFileStore } from './stores/fileStore'
import { usePermissionStore } from './stores/permissionStore'

// 通用组件
import Breadcrumb from './components/common/Breadcrumb.vue'
import SearchBar from './components/common/SearchBar.vue'
import FilterPanel from './components/common/FilterPanel.vue'
import Pagination from './components/common/Pagination.vue'

// 目录组件
import DirectoryTree from './components/directory/DirectoryTree.vue'
import DirectoryList from './components/directory/DirectoryList.vue'
import CreateDirectoryDialog from './components/directory/CreateDirectoryDialog.vue'
import RenameDirectoryDialog from './components/directory/RenameDirectoryDialog.vue'
import MoveDirectoryDialog from './components/directory/MoveDirectoryDialog.vue'

// 文件组件
import FileList from './components/file/FileList.vue'
import FilePreview from './components/file/FilePreview.vue'

// 注入基础服务
const $baseMessage = inject('$baseMessage')
const $baseConfirm = inject('$baseConfirm')

// 状态管理
const fmsStore = useFmsStore()
const directoryStore = useDirectoryStore()
const fileStore = useFileStore()
const permissionStore = usePermissionStore()

// 响应式数据
const searchKeyword = ref('')
const currentDirectoryId = ref(null)
const selectedDirectory = ref(null)
const previewFile = ref(null)
const treeHeight = ref('calc(100vh - 200px)')

// 筛选参数
const filterParams = reactive({
  name: '',
  type: '',
  tags: [],
  dateRange: []
})

// 分页参数
const pagination = reactive({
  total: 0,
  page: 1,
  pageSize: 20
})

// 对话框状态
const dialogs = reactive({
  createDirectory: false,
  renameDirectory: false,
  moveDirectory: false
})

// 计算属性
const breadcrumbItems = computed(() => {
  return fmsStore.breadcrumbPath
})

const showDirectoryList = computed(() => {
  return fmsStore.viewMode === 'directory' || fmsStore.viewMode === 'mixed'
})

const showFileList = computed(() => {
  return fmsStore.viewMode === 'file' || fmsStore.viewMode === 'mixed'
})

const filterFields = computed(() => [
  {
    prop: 'name',
    label: '名称',
    type: 'input',
    placeholder: '请输入名称'
  },
  {
    prop: 'type',
    label: '类型',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '目录', value: 'directory' },
      { label: '文件', value: 'file' }
    ]
  },
  {
    prop: 'tags',
    label: '标签',
    type: 'select',
    multiple: true,
    options: []
  },
  {
    prop: 'dateRange',
    label: '创建时间',
    type: 'daterange'
  }
])

// 方法
const onBreadcrumbChange = (item) => {
  if (item.to) {
    currentDirectoryId.value = item.id
    fmsStore.setCurrentDirectory(item.id)
  }
}

const onSearch = (keyword) => {
  fmsStore.setSearchKeyword(keyword)
  refreshData()
}

const onDirectorySelect = (node) => {
  currentDirectoryId.value = node.id
  fmsStore.setCurrentDirectory(node.id)
  refreshData()
}

const onFilterSubmit = (params) => {
  Object.assign(filterParams, params)
  pagination.page = 1
  refreshData()
}

const onFilterReset = () => {
  Object.keys(filterParams).forEach(key => {
    if (Array.isArray(filterParams[key])) {
      filterParams[key] = []
    } else {
      filterParams[key] = ''
    }
  })
  pagination.page = 1
  refreshData()
}

const onPaginationChange = ({ page, pageSize }) => {
  pagination.page = page
  pagination.pageSize = pageSize
  refreshData()
}

const refreshData = async () => {
  await Promise.all([
    refreshDirectoryList(),
    refreshFileList()
  ])
}

const refreshDirectoryList = async () => {
  if (!showDirectoryList.value) return
  
  try {
    const result = await directoryStore.fetchDirectories({
      parentId: currentDirectoryId.value,
      ...filterParams,
      page: pagination.page,
      pageSize: pagination.pageSize
    })
    pagination.total = result.total
  } catch (error) {
    $baseMessage('获取目录列表失败', 'error', 'vab-hey-message-error')
  }
}

const refreshFileList = async () => {
  if (!showFileList.value) return
  
  try {
    const result = await fileStore.fetchFiles({
      directoryId: currentDirectoryId.value,
      ...filterParams,
      page: pagination.page,
      pageSize: pagination.pageSize
    })
    pagination.total = result.total
  } catch (error) {
    $baseMessage('获取文件列表失败', 'error', 'vab-hey-message-error')
  }
}

// 目录操作
const onDirectoryRename = (directory) => {
  selectedDirectory.value = directory
  dialogs.renameDirectory = true
}

const onDirectoryMove = (directory) => {
  selectedDirectory.value = directory
  dialogs.moveDirectory = true
}

const onDirectoryCreated = () => {
  dialogs.createDirectory = false
  refreshDirectoryList()
  $baseMessage('目录创建成功', 'success', 'vab-hey-message-success')
}

const onDirectoryRenamed = () => {
  dialogs.renameDirectory = false
  selectedDirectory.value = null
  refreshDirectoryList()
  $baseMessage('目录重命名成功', 'success', 'vab-hey-message-success')
}

const onDirectoryMoved = () => {
  dialogs.moveDirectory = false
  selectedDirectory.value = null
  refreshDirectoryList()
  $baseMessage('目录移动成功', 'success', 'vab-hey-message-success')
}

// 文件操作
const onFilePreview = (file) => {
  previewFile.value = file
}

const onFileDelete = async (file) => {
  $baseConfirm(
    `确认删除文件 "${file.name}"？`,
    () => {},
    async () => {
      try {
        await fileStore.deleteFile(file.id)
        refreshFileList()
        $baseMessage('文件删除成功', 'success', 'vab-hey-message-success')
      } catch (error) {
        $baseMessage('文件删除失败', 'error', 'vab-hey-message-error')
      }
    }
  )
}

const closeFilePreview = () => {
  previewFile.value = null
}

// 生命周期
onMounted(async () => {
  await fmsStore.initialize()
  await refreshData()
})
</script>

<style lang="scss" scoped>
.fms-main {
  height: 100vh;
  
  &__container {
    height: 100%;
  }
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color);
  }
  
  &__toolbar {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  &__body {
    height: calc(100% - 60px);
  }
  
  &__aside {
    border-right: 1px solid var(--el-border-color-light);
    background: var(--el-bg-color);
    overflow: hidden;
  }
  
  &__main {
    padding: 16px;
    overflow: auto;
  }
}
</style>
