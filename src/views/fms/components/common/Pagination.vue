<template>
  <div class="fms-pagination">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="currentPageSize"
      :total="total"
      :page-sizes="pageSizes"
      :layout="layout"
      :background="background"
      :small="small"
      :disabled="disabled"
      class="fms-pagination__component"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <div v-if="showInfo" class="fms-pagination__info">
      <span class="fms-pagination__info-text">
        共 {{ total }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
      </span>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch, defineProps, defineEmits } from 'vue'

  // Props 定义
  const props = defineProps({
    total: {
      type: Number,
      default: 0,
    },
    page: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 20,
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100],
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper',
    },
    background: {
      type: Boolean,
      default: true,
    },
    small: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    showInfo: {
      type: Boolean,
      default: false,
    },
    align: {
      type: String,
      default: 'left',
      validator: (value) => ['left', 'center', 'right'].includes(value),
    },
  })

  // Events 定义
  const emit = defineEmits(['change', 'size-change', 'current-change'])

  // 响应式数据
  const currentPage = ref(props.page)
  const currentPageSize = ref(props.pageSize)

  // 计算属性
  const totalPages = computed(() => {
    return Math.ceil(props.total / currentPageSize.value)
  })

  // 计算对齐方式
  const justifyContent = computed(() => {
    return props.align === 'left' ? 'flex-start' : props.align === 'right' ? 'flex-end' : 'center'
  })

  // 监听 props 变化
  watch(
    () => props.page,
    (newValue) => {
      currentPage.value = newValue
    }
  )

  watch(
    () => props.pageSize,
    (newValue) => {
      currentPageSize.value = newValue
    }
  )

  // 方法
  const handleSizeChange = (size) => {
    currentPageSize.value = size
    currentPage.value = 1 // 改变页大小时重置到第一页

    const changeData = {
      page: currentPage.value,
      pageSize: currentPageSize.value,
    }

    emit('size-change', size)
    emit('change', changeData)
  }

  const handleCurrentChange = (page) => {
    currentPage.value = page

    const changeData = {
      page: currentPage.value,
      pageSize: currentPageSize.value,
    }

    emit('current-change', page)
    emit('change', changeData)
  }

  // 公开方法
  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      handleCurrentChange(page)
    }
  }

  const goToFirst = () => {
    goToPage(1)
  }

  const goToLast = () => {
    goToPage(totalPages.value)
  }

  const refresh = () => {
    const changeData = {
      page: currentPage.value,
      pageSize: currentPageSize.value,
    }
    emit('change', changeData)
  }

  // 暴露方法给父组件
  defineExpose({
    goToPage,
    goToFirst,
    goToLast,
    refresh,
    currentPage,
    currentPageSize,
    totalPages,
  })
</script>

<style lang="scss" scoped>
  .fms-pagination {
    display: flex;
    align-items: center;
    justify-content: v-bind(justifyContent);
    gap: 16px;
    margin-top: 16px;
    padding: 16px 0;

    &__component {
      :deep(.el-pagination) {
        --el-pagination-font-size: 14px;
        --el-pagination-bg-color: var(--el-bg-color);
        --el-pagination-text-color: var(--el-text-color-primary);
        --el-pagination-border-radius: 4px;
        --el-pagination-button-color: var(--el-text-color-primary);
        --el-pagination-button-bg-color: var(--el-bg-color);
        --el-pagination-button-disabled-color: var(--el-text-color-placeholder);
        --el-pagination-button-disabled-bg-color: var(--el-bg-color-disabled);
        --el-pagination-hover-color: var(--el-color-primary);
        --el-pagination-hover-bg-color: var(--el-color-primary-light-9);
      }

      :deep(.el-pager li) {
        min-width: 32px;
        height: 32px;
        line-height: 30px;
        margin: 0 2px;
        border: 1px solid var(--el-border-color-light);
        border-radius: 4px;

        &:hover {
          color: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }

        &.is-active {
          color: var(--el-color-white);
          background-color: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }
      }

      :deep(.btn-prev),
      :deep(.btn-next) {
        width: 32px;
        height: 32px;
        border: 1px solid var(--el-border-color-light);
        border-radius: 4px;

        &:hover {
          color: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }

        &:disabled {
          color: var(--el-text-color-placeholder);
          border-color: var(--el-border-color-lighter);
          cursor: not-allowed;
        }
      }
    }

    &__info {
      &-text {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }

  // 响应式布局
  @media (max-width: 768px) {
    .fms-pagination {
      flex-direction: column;
      gap: 8px;

      &__component {
        :deep(.el-pagination) {
          .el-pagination__sizes,
          .el-pagination__jump {
            display: none;
          }
        }
      }
    }
  }
</style>
