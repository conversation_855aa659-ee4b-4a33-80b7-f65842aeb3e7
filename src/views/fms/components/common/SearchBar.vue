<template>
  <div class="fms-search-bar">
    <el-input
      v-model="inputValue"
      :placeholder="placeholder"
      :clearable="clearable"
      :disabled="disabled"
      class="fms-search-bar__input"
      @keyup.enter="handleSearch"
      @clear="handleClear"
    >
      <template #prefix>
        <el-icon class="fms-search-bar__prefix-icon">
          <Search />
        </el-icon>
      </template>
      <template #suffix>
        <el-button
          v-if="showSearchButton"
          type="primary"
          :icon="Search"
          :loading="loading"
          :disabled="disabled"
          class="fms-search-bar__button"
          @click="handleSearch"
        >
          {{ searchButtonText }}
        </el-button>
      </template>
    </el-input>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'
import { Search } from '@element-plus/icons-vue'

// Props 定义
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入关键字'
  },
  clearable: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  showSearchButton: {
    type: Boolean,
    default: true
  },
  searchButtonText: {
    type: String,
    default: '搜索'
  },
  debounce: {
    type: Number,
    default: 300
  }
})

// Events 定义
const emit = defineEmits(['update:modelValue', 'search', 'clear'])

// 响应式数据
const inputValue = ref(props.modelValue)
let debounceTimer = null

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue
})

// 监听输入值变化
watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
  
  // 防抖搜索
  if (props.debounce > 0) {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }
    debounceTimer = setTimeout(() => {
      if (newValue.trim()) {
        emit('search', newValue.trim())
      }
    }, props.debounce)
  }
})

// 方法
const handleSearch = () => {
  const value = inputValue.value.trim()
  if (value) {
    emit('search', value)
  }
}

const handleClear = () => {
  inputValue.value = ''
  emit('clear')
}
</script>

<style lang="scss" scoped>
.fms-search-bar {
  &__input {
    :deep(.el-input__wrapper) {
      border-radius: 20px;
    }
  }
  
  &__prefix-icon {
    color: var(--el-text-color-placeholder);
  }
  
  &__button {
    border-radius: 0 16px 16px 0;
    border-left: none;
    
    &:hover {
      border-left: none;
    }
  }
}

:deep(.el-input-group__append) {
  padding: 0;
  border-left: none;
  background: transparent;
  
  .fms-search-bar__button {
    margin: 0;
  }
}
</style>
