<template>
  <div class="fms-directory-tree">
    <div class="fms-directory-tree__header">
      <div class="fms-directory-tree__title">
        <el-icon><Folder /></el-icon>
        <span>目录结构</span>
      </div>
      <div class="fms-directory-tree__actions">
        <el-button
          type="text"
          :icon="Refresh"
          size="small"
          @click="refreshTree"
        />
      </div>
    </div>

    <div
      class="fms-directory-tree__content"
      :style="{ height: height }"
      v-loading="isLoading"
    >
      <a-tree
        v-if="showTree && treeData.length > 0"
        ref="treeRef"
        :tree-data="treeData"
        :height="treeHeight"
        :field-names="fieldNames"
        :selected-keys="selectedKeys"
        :expanded-keys="expandedKeys"
        :load-data="loadData"
        :draggable="draggable"
        :show-line="showLine"
        :show-icon="showIcon"
        block-node
        @select="handleSelect"
        @expand="handleExpand"
        @drop="handleDrop"
      >
        <template #title="{ title, key, isLeaf, permissions }">
          <div class="fms-directory-tree__node">
            <div class="fms-directory-tree__node-content">
              <el-icon class="fms-directory-tree__node-icon">
                <Folder v-if="!isLeaf" />
                <Document v-else />
              </el-icon>
              <span class="fms-directory-tree__node-title">{{ title }}</span>
            </div>
            <div class="fms-directory-tree__node-actions">
              <el-dropdown trigger="click" @command="handleCommand">
                <el-button
                  type="text"
                  :icon="MoreFilled"
                  size="small"
                  class="fms-directory-tree__node-more"
                />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :command="{ action: 'create', nodeKey: key }"
                      :disabled="!canCreate(permissions)"
                    >
                      <el-icon><Plus /></el-icon>
                      新建子目录
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'rename', nodeKey: key }"
                      :disabled="!canRename(permissions)"
                    >
                      <el-icon><Edit /></el-icon>
                      重命名
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'move', nodeKey: key }"
                      :disabled="!canMove(permissions)"
                    >
                      <el-icon><Rank /></el-icon>
                      移动
                    </el-dropdown-item>
                    <el-dropdown-item
                      :command="{ action: 'delete', nodeKey: key }"
                      :disabled="!canDelete(permissions)"
                      divided
                    >
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </template>
      </a-tree>

      <div v-else-if="!isLoading" class="fms-directory-tree__empty">
        <el-empty description="暂无目录数据" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    computed,
    watch,
    onMounted,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import {
    Folder,
    Document,
    Refresh,
    MoreFilled,
    Plus,
    Edit,
    Rank,
    Delete,
  } from '@element-plus/icons-vue'
  import { useDirectoryStore } from '../../stores/directoryStore'
  import { useFmsPermission } from '@/utils/bmsPermission'

  // Props 定义
  const props = defineProps({
    selectedId: {
      type: [Number, String],
      default: null,
    },
    rootId: {
      type: [Number, String],
      default: null,
    },
    height: {
      type: String,
      default: '100%',
    },
    draggable: {
      type: Boolean,
      default: true,
    },
    showLine: {
      type: Boolean,
      default: true,
    },
    showIcon: {
      type: Boolean,
      default: true,
    },
  })

  // Events 定义
  const emit = defineEmits(['select', 'create', 'rename', 'move', 'delete'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // Store
  const directoryStore = useDirectoryStore()
  const { FMS_PERMISSIONS, can } = useFmsPermission()

  // 响应式数据
  const treeRef = ref()
  const showTree = ref(true)
  const selectedKeys = ref([])
  const expandedKeys = ref([])

  // 计算属性
  const isLoading = computed(() => directoryStore.isTreeLoading)
  const treeData = computed(() => directoryStore.treeData)
  const treeHeight = computed(() => {
    // 计算树的实际高度
    const headerHeight = 40
    const padding = 16
    return `calc(${props.height} - ${headerHeight + padding}px)`
  })

  // 字段映射配置
  const fieldNames = {
    key: 'id',
    title: 'name',
    children: 'children',
  }

  // 监听选中ID变化
  watch(
    () => props.selectedId,
    (newId) => {
      if (newId) {
        selectedKeys.value = [String(newId)]
      } else {
        selectedKeys.value = []
      }
    }
  )

  // 方法
  const loadTreeData = async () => {
    try {
      await directoryStore.fetchDirectoryTree({
        rootId: props.rootId,
        includePermissions: true,
      })

      // 设置初始选中
      if (props.selectedId) {
        selectedKeys.value = [String(props.selectedId)]
      }

      // 设置初始展开
      if (treeData.value.length > 0) {
        expandedKeys.value = [String(treeData.value[0].id)]
      }
    } catch (error) {
      $baseMessage('加载目录树失败', 'error', 'vab-hey-message-error')
    }
  }

  const refreshTree = async () => {
    showTree.value = false
    await loadTreeData()
    showTree.value = true
  }

  const loadData = async (treeNode) => {
    // 异步加载子节点数据
    const { key } = treeNode.dataRef

    try {
      const children = await directoryStore.fetchDirectories({
        parentId: key,
        includePermissions: true,
      })

      // 更新树节点
      treeNode.dataRef.children = children.data.map((dir) => ({
        id: dir.id,
        name: dir.name,
        key: String(dir.id),
        title: dir.name,
        isLeaf: dir.childrenCount === 0,
        permissions: dir.permissions,
        children: dir.childrenCount > 0 ? [] : undefined,
      }))
    } catch (error) {
      $baseMessage('加载子目录失败', 'error', 'vab-hey-message-error')
    }
  }

  const handleSelect = (selectedKeys, { node }) => {
    if (selectedKeys.length > 0) {
      const nodeData = {
        id: Number(node.key),
        name: node.title,
        key: node.key,
        ...node.dataRef,
      }
      emit('select', nodeData)
    }
  }

  const handleExpand = (expandedKeys) => {
    expandedKeys.value = expandedKeys
  }

  const handleDrop = async ({ node, dragNode, dropPosition }) => {
    if (!props.draggable) return

    try {
      const dragId = Number(dragNode.key)
      const dropId = Number(node.key)

      let newParentId = null

      if (dropPosition === 0) {
        // 拖拽到节点内部
        newParentId = dropId
      } else {
        // 拖拽到节点同级
        newParentId = node.dataRef.parentId
      }

      await directoryStore.moveDirectory(dragId, newParentId)

      $baseMessage('目录移动成功', 'success', 'vab-hey-message-success')

      // 刷新树
      await refreshTree()
    } catch (error) {
      $baseMessage('目录移动失败', 'error', 'vab-hey-message-error')
    }
  }

  const handleCommand = ({ action, nodeKey }) => {
    const nodeId = Number(nodeKey)

    switch (action) {
      case 'create':
        emit('create', { parentId: nodeId })
        break
      case 'rename':
        emit('rename', { id: nodeId })
        break
      case 'move':
        emit('move', { id: nodeId })
        break
      case 'delete':
        handleDelete(nodeId)
        break
    }
  }

  const handleDelete = (nodeId) => {
    $baseConfirm(
      '确认删除该目录？删除后将移至回收站。',
      () => {},
      async () => {
        try {
          await directoryStore.deleteDirectory(nodeId)
          $baseMessage('目录删除成功', 'success', 'vab-hey-message-success')

          // 刷新树
          await refreshTree()

          emit('delete', { id: nodeId })
        } catch (error) {
          $baseMessage('目录删除失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  // 权限检查方法
  const canCreate = (permissions) => {
    return (
      can('directory', null, FMS_PERMISSIONS.MANAGE) ||
      permissions & FMS_PERMISSIONS.MANAGE
    )
  }

  const canRename = (permissions) => {
    return (
      can('directory', null, FMS_PERMISSIONS.MANAGE) ||
      permissions & FMS_PERMISSIONS.MANAGE
    )
  }

  const canMove = (permissions) => {
    return (
      can('directory', null, FMS_PERMISSIONS.MANAGE) ||
      permissions & FMS_PERMISSIONS.MANAGE
    )
  }

  const canDelete = (permissions) => {
    return (
      can('directory', null, FMS_PERMISSIONS.DELETE) ||
      permissions & FMS_PERMISSIONS.DELETE
    )
  }

  // 生命周期
  onMounted(() => {
    loadTreeData()
  })
</script>

<style lang="scss" scoped>
  .fms-directory-tree {
    height: 100%;
    display: flex;
    flex-direction: column;

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      background: var(--el-bg-color-page);
    }

    &__title {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    &__actions {
      display: flex;
      align-items: center;
    }

    &__content {
      flex: 1;
      overflow: auto;
      padding: 8px;
    }

    &__node {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 2px 4px;

      &:hover {
        .fms-directory-tree__node-actions {
          opacity: 1;
        }
      }
    }

    &__node-content {
      display: flex;
      align-items: center;
      gap: 6px;
      flex: 1;
      min-width: 0;
    }

    &__node-icon {
      font-size: 14px;
      color: var(--el-color-primary);
    }

    &__node-title {
      font-size: 13px;
      color: var(--el-text-color-primary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &__node-actions {
      opacity: 0;
      transition: opacity 0.2s;
    }

    &__node-more {
      padding: 2px;
      font-size: 12px;
    }

    &__empty {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
    }
  }

  // antd-vue 树组件样式覆盖
  :deep(.ant-tree) {
    background: transparent;

    .ant-tree-node-content-wrapper {
      padding: 0;
      border-radius: 4px;

      &:hover {
        background-color: var(--el-color-primary-light-9);
      }

      &.ant-tree-node-selected {
        background-color: var(--el-color-primary-light-8);
      }
    }

    .ant-tree-title {
      width: 100%;
    }

    .ant-tree-switcher {
      color: var(--el-text-color-regular);

      &:hover {
        background-color: var(--el-fill-color-light);
      }
    }

    .ant-tree-indent-unit {
      width: 16px;
    }
  }
</style>
