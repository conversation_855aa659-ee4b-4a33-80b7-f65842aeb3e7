<template>
  <div class="fms-directory-list">
    <div class="fms-directory-list__toolbar">
      <div class="fms-directory-list__toolbar-left">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleCreate"
          v-fms-permission="{
            targetType: 'directory',
            targetId: parentId,
            permission: FMS_PERMISSIONS.MANAGE,
          }"
        >
          新建目录
        </el-button>
        <el-button :icon="Refresh" @click="handleRefresh">刷新</el-button>
      </div>

      <div class="fms-directory-list__toolbar-right">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="list">
            <el-icon><List /></el-icon>
            列表
          </el-radio-button>
          <el-radio-button label="grid">
            <el-icon><Grid /></el-icon>
            网格
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 列表视图 -->
    <div v-if="viewMode === 'list'" class="fms-directory-list__table">
      <el-table
        ref="tableRef"
        :data="directories"
        v-loading="isLoading"
        row-key="id"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="名称" min-width="200">
          <template #default="{ row }">
            <div class="fms-directory-list__name">
              <el-icon class="fms-directory-list__icon">
                <Folder />
              </el-icon>
              <span class="fms-directory-list__title">{{ row.name }}</span>
              <el-tag
                v-if="row.visibility === 'private'"
                type="warning"
                size="small"
                class="fms-directory-list__visibility"
              >
                私有
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="子目录" width="80" align="center">
          <template #default="{ row }">
            <span class="fms-directory-list__count">
              {{ row.childrenCount || 0 }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="文件数" width="80" align="center">
          <template #default="{ row }">
            <span class="fms-directory-list__count">
              {{ row.filesCount || 0 }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="大小" width="100" align="right">
          <template #default="{ row }">
            <span class="fms-directory-list__size">
              {{ formatFileSize(row.totalSize || 0) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="创建者" width="120">
          <template #default="{ row }">
            <div class="fms-directory-list__owner">
              <el-avatar
                v-if="row.owner?.avatar"
                :src="row.owner.avatar"
                :size="24"
              />
              <span>{{ row.owner?.name || '-' }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            <span class="fms-directory-list__time">
              {{ formatDateTime(row.createdAt) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="fms-directory-list__actions">
              <el-button
                type="text"
                size="small"
                @click.stop="handleRename(row)"
                v-fms-permission="{
                  targetType: 'directory',
                  targetId: row.id,
                  permission: FMS_PERMISSIONS.MANAGE,
                }"
              >
                重命名
              </el-button>
              <el-button
                type="text"
                size="small"
                @click.stop="handleMove(row)"
                v-fms-permission="{
                  targetType: 'directory',
                  targetId: row.id,
                  permission: FMS_PERMISSIONS.MANAGE,
                }"
              >
                移动
              </el-button>
              <el-dropdown @command="(command) => handleCommand(command, row)">
                <el-button type="text" size="small">
                  更多
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="visibility">
                      设置可见性
                    </el-dropdown-item>
                    <el-dropdown-item command="permission">
                      权限管理
                    </el-dropdown-item>
                    <el-dropdown-item
                      command="delete"
                      divided
                      v-fms-permission="{
                        targetType: 'directory',
                        targetId: row.id,
                        permission: FMS_PERMISSIONS.DELETE,
                      }"
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 网格视图 -->
    <div v-else class="fms-directory-list__grid">
      <div
        v-for="directory in directories"
        :key="directory.id"
        class="fms-directory-list__grid-item"
        @click="handleRowClick(directory)"
        @contextmenu.prevent="handleContextMenu($event, directory)"
      >
        <div class="fms-directory-list__grid-icon">
          <el-icon><Folder /></el-icon>
        </div>
        <div class="fms-directory-list__grid-content">
          <div class="fms-directory-list__grid-title">{{ directory.name }}</div>
          <div class="fms-directory-list__grid-info">
            <span>{{ directory.childrenCount || 0 }}个子目录</span>
            <span>{{ directory.filesCount || 0 }}个文件</span>
          </div>
          <div class="fms-directory-list__grid-time">
            {{ formatDateTime(directory.createdAt) }}
          </div>
        </div>
        <div class="fms-directory-list__grid-actions">
          <el-dropdown
            @command="(command) => handleCommand(command, directory)"
          >
            <el-button type="text" :icon="MoreFilled" size="small" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="rename">重命名</el-dropdown-item>
                <el-dropdown-item command="move">移动</el-dropdown-item>
                <el-dropdown-item command="delete" divided>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div
      v-if="!isLoading && directories.length === 0"
      class="fms-directory-list__empty"
    >
      <el-empty description="暂无目录数据">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleCreate"
          v-fms-permission="{
            targetType: 'directory',
            targetId: parentId,
            permission: FMS_PERMISSIONS.MANAGE,
          }"
        >
          新建目录
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    computed,
    watch,
    onMounted,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import {
    Plus,
    Refresh,
    List,
    Grid,
    Folder,
    ArrowDown,
    MoreFilled,
  } from '@element-plus/icons-vue'
  import { useDirectoryStore } from '../../stores/directoryStore'
  import { useFmsStore } from '../../stores/fmsStore'
  import { useFmsPermission, vFmsPermission } from '@/utils/bmsPermission'

  // Props 定义
  const props = defineProps({
    parentId: {
      type: [Number, String],
      default: null,
    },
    filters: {
      type: Object,
      default: () => ({}),
    },
  })

  // Events 定义
  const emit = defineEmits([
    'refresh',
    'rename',
    'move',
    'delete',
    'select',
    'create',
  ])

  // 注入服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // Store
  const directoryStore = useDirectoryStore()
  const fmsStore = useFmsStore()
  const { FMS_PERMISSIONS } = useFmsPermission()

  // 响应式数据
  const tableRef = ref()
  const viewMode = ref('list')
  const selectedDirectories = ref([])

  // 计算属性
  const isLoading = computed(() => directoryStore.isLoading)
  const directories = computed(() => directoryStore.directories)

  // 监听父目录变化
  watch(
    () => props.parentId,
    () => {
      loadDirectories()
    }
  )

  // 监听筛选条件变化
  watch(
    () => props.filters,
    () => {
      loadDirectories()
    },
    { deep: true }
  )

  // 方法
  const loadDirectories = async () => {
    try {
      await directoryStore.fetchDirectories({
        parentId: props.parentId,
        ...props.filters,
        includeStats: true,
        includeOwner: true,
      })
    } catch (error) {
      $baseMessage('加载目录列表失败', 'error', 'vab-hey-message-error')
    }
  }

  const handleCreate = () => {
    emit('create', { parentId: props.parentId })
  }

  const handleRefresh = () => {
    emit('refresh')
    loadDirectories()
  }

  const handleSelectionChange = (selection) => {
    selectedDirectories.value = selection
  }

  const handleRowClick = (row) => {
    emit('select', row)
  }

  const handleRename = (row) => {
    emit('rename', row)
  }

  const handleMove = (row) => {
    emit('move', row)
  }

  const handleCommand = (command, row) => {
    switch (command) {
      case 'rename':
        handleRename(row)
        break
      case 'move':
        handleMove(row)
        break
      case 'visibility':
        handleVisibility(row)
        break
      case 'permission':
        handlePermission(row)
        break
      case 'delete':
        handleDelete(row)
        break
    }
  }

  const handleDelete = (row) => {
    $baseConfirm(
      `确认删除目录 "${row.name}"？删除后将移至回收站。`,
      () => {},
      async () => {
        try {
          await directoryStore.deleteDirectory(row.id)
          $baseMessage('目录删除成功', 'success', 'vab-hey-message-success')
          emit('delete', row)
          await loadDirectories()
        } catch (error) {
          $baseMessage('目录删除失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handleVisibility = (row) => {
    // TODO: 实现可见性设置对话框
    $baseMessage('可见性设置功能开发中', 'info', 'vab-hey-message-info')
  }

  const handlePermission = (row) => {
    // TODO: 实现权限管理对话框
    $baseMessage('权限管理功能开发中', 'info', 'vab-hey-message-info')
  }

  const handleContextMenu = (event, row) => {
    // TODO: 实现右键菜单
    console.log('Context menu for:', row)
  }

  // 工具方法
  const formatFileSize = (bytes) => {
    return fmsStore.formatFileSize(bytes)
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 生命周期
  onMounted(() => {
    loadDirectories()
  })

  // 暴露方法
  defineExpose({
    refresh: loadDirectories,
    getSelectedDirectories: () => selectedDirectories.value,
  })
</script>

<style lang="scss" scoped>
  .fms-directory-list {
    &__toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 12px 0;

      &-left,
      &-right {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    &__table {
      .el-table {
        border: 1px solid var(--el-border-color-light);
        border-radius: 6px;
      }
    }

    &__name {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    &__icon {
      font-size: 16px;
      color: var(--el-color-primary);
    }

    &__title {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    &__visibility {
      margin-left: 8px;
    }

    &__count,
    &__size {
      font-family: 'Monaco', 'Menlo', monospace;
      color: var(--el-text-color-regular);
    }

    &__owner {
      display: flex;
      align-items: center;
      gap: 6px;

      span {
        font-size: 13px;
        color: var(--el-text-color-regular);
      }
    }

    &__time {
      font-size: 13px;
      color: var(--el-text-color-regular);
    }

    &__actions {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    // 网格视图样式
    &__grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;
      padding: 16px 0;
    }

    &__grid-item {
      position: relative;
      padding: 16px;
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: var(--el-color-primary);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .fms-directory-list__grid-actions {
          opacity: 1;
        }
      }
    }

    &__grid-icon {
      text-align: center;
      margin-bottom: 12px;

      .el-icon {
        font-size: 32px;
        color: var(--el-color-primary);
      }
    }

    &__grid-content {
      text-align: center;
    }

    &__grid-title {
      font-weight: 500;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &__grid-info {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-bottom: 4px;

      span {
        display: block;
      }
    }

    &__grid-time {
      font-size: 11px;
      color: var(--el-text-color-placeholder);
    }

    &__grid-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      opacity: 0;
      transition: opacity 0.2s;
    }

    &__empty {
      padding: 40px 0;
      text-align: center;
    }
  }
</style>
