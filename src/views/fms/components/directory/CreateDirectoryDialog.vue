<template>
  <firefly-dialog
    v-model="dialogVisible"
    :title="props.isEditMode ? '目录设置' : '新建目录'"
    width="518px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="父目录" prop="parentPath">
        <el-input v-model="parentPath" readonly placeholder="根目录" disabled>
          <template #prefix>
            <vab-icon
              :icon="'icon-dir'"
              is-custom-svg
              :style="{
                width: 16 + 'px',
                height: 16 + 'px',
                marginRight: '6px',
              }"
            />
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="目录名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入目录名称"
          maxlength="100"
          show-word-limit
          @keyup.enter="handleSubmit"
        />
      </el-form-item>

      <el-form-item label="可见性" prop="visibility">
        <el-radio-group v-model="formData.visibility">
          <el-radio label="public">
            <el-icon><View /></el-icon>
            公开
          </el-radio>
          <el-radio label="department">
            <vab-icon
              icon="department"
              is-custom-svg
              style="width: 22px; height: 22px; margin-right: 0px"
            />
            部门
          </el-radio>
          <el-radio label="user">
            <vab-icon icon="admin-line" />
            用户
          </el-radio>
          <el-radio label="private">
            <vab-icon icon="lock-2-line" />
            私有
          </el-radio>
        </el-radio-group>
        <div class="create-directory-dialog__visibility-help">
          <el-text size="small" type="info">
            公开：所有人可见；部门：仅指定部门内可见；用户：仅指定用户可见；私有：仅创建者可见
          </el-text>
        </div>
      </el-form-item>

      <!-- 用户选择 -->
      <el-form-item v-if="formData.visibility === 'user'" label="选择用户" prop="selectedUsers">
        <template v-if="formData.selectedUsers && formData.selectedUsers.length > 0">
          <span
            v-for="(user, index) in formData.selectedUsers"
            :key="index"
            class="create-directory-dialog__user-item"
          >
            {{ user.name || user.username }}
            <vab-icon
              icon="close-fill"
              @click="removeUser(user.id)"
            />
          </span>
        </template>
        <vab-icon
          icon="add-circle-fill"
          style="font-size: 20px; color: #1890ff; cursor: pointer"
          @click="handleUserSelect"
        />
      </el-form-item>

      <!-- 部门选择 -->
      <el-form-item v-if="formData.visibility === 'department'" label="选择部门" prop="selectedDepartments">
        <el-cascader
          v-model="formData.selectedDepartments"
          :options="departmentOptions"
          clearable
          filterable
          collapse-tags
          :show-all-levels="false"
          :props="{
            expandTrigger: 'hover',
            multiple: true,
            value: 'id',
            label: 'name',
            emitPath: false,
          }"
          placeholder="请选择部门"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 私有用户显示 -->
      <el-form-item v-if="formData.visibility === 'private'" label="选择用户" prop="privateUser">
        <el-input
          v-model="currentUserName"
          readonly
          disabled
          placeholder="当前用户"
        >
          <template #prefix>
            <vab-icon icon="admin-line" />
          </template>
        </el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="create-directory-dialog__footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="isSubmitting" @click="handleSubmit">
          {{ props.isEditMode ? '保存设置' : '创建目录' }}
        </el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import {
    ref,
    reactive,
    computed,
    watch,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import {
    Folder,
    FolderAdd,
    View,
    Hide,
    User,
    OfficeBuilding,
  } from '@element-plus/icons-vue'
  import { storeToRefs } from 'pinia'
  import { useDirectoryStore } from '../../stores/directoryStore'
  import { useUserStore } from '@/store/modules/user'

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    parentId: {
      type: [Number, String],
      default: null,
    },
    parentPath: {
      type: String,
      default: '',
    },
    // 编辑模式相关props
    isEditMode: {
      type: Boolean,
      default: false,
    },
    directoryId: {
      type: [Number, String],
      default: null,
    },
    directoryName: {
      type: String,
      default: '',
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible', 'success'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const directoryStore = useDirectoryStore()
  const userStore = useUserStore()
  const { user_id, userInfo } = storeToRefs(userStore)

  // 响应式数据
  const formRef = ref()
  const isSubmitting = ref(false)
  const departmentOptions = ref([])

  const formData = reactive({
    name: '',
    visibility: 'public',
    sortOrder: 0,
    selectedUsers: [],
    selectedDepartments: [],
    targetType: '', // 'user' 或 'department'
    targetIds: [], // 用户ID或部门ID列表
  })

  // 当前用户名称
  const currentUserName = computed(() => {
    return userInfo.value?.username || userInfo.value?.name || '当前用户'
  })

  // 表单验证规则
  const formRules = {
    name: [
      { required: true, message: '请输入目录名称', trigger: 'blur' },
      {
        min: 1,
        max: 100,
        message: '目录名称长度在 1 到 100 个字符',
        trigger: 'blur',
      },
      {
        pattern: /^[^<>:"/\\|?*]+$/,
        message: '目录名称不能包含特殊字符 < > : " / \\ | ? *',
        trigger: 'blur',
      },
    ],
    visibility: [
      { required: true, message: '请选择可见性', trigger: 'change' },
    ],
    sortOrder: [
      {
        type: 'number',
        min: 0,
        max: 9999,
        message: '排序值范围为 0-9999',
        trigger: 'blur',
      },
    ],
  }

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  const parentPath = computed(() => {
    if (props.parentPath) {
      return props.parentPath
    }

    if (!props.parentId) return '根目录'

    // 从store获取父目录路径
    const parentDirectory = directoryStore.directoryMap.get(props.parentId)
    return parentDirectory ? parentDirectory.path : '未知目录'
  })

  const displayPath = computed(() => {
    if (props.isEditMode) {
      return props.directoryName || '当前目录'
    }
    return parentPath.value
  })

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible) {
        if (props.isEditMode) {
          loadDirectoryData()
        } else {
          resetForm()
        }
      }
    }
  )

  // 方法
  const resetForm = () => {
    formData.name = ''
    formData.visibility = 'public'
    formData.sortOrder = 0
    formData.selectedUsers = []
    formData.selectedDepartments = []
    formData.targetType = ''
    formData.targetIds = []

    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  // 用户选择相关方法
  const handleUserSelect = () => {
    // TODO: 实现用户选择弹窗逻辑
    console.log('打开用户选择弹窗')
  }

  const removeUser = (userId) => {
    formData.selectedUsers = formData.selectedUsers.filter(user => user.id !== userId)
  }

  // 监听可见性变化，更新targetType
  watch(
    () => formData.visibility,
    (newVisibility) => {
      if (newVisibility === 'user') {
        formData.targetType = 'user'
        formData.selectedDepartments = []
      } else if (newVisibility === 'department') {
        formData.targetType = 'department'
        formData.selectedUsers = []
      } else if (newVisibility === 'private') {
        formData.targetType = 'user'
        formData.targetIds = [user_id.value]
        formData.selectedUsers = []
        formData.selectedDepartments = []
      } else {
        formData.targetType = ''
        formData.targetIds = []
        formData.selectedUsers = []
        formData.selectedDepartments = []
      }
    }
  )

  const loadDirectoryData = async () => {
    if (!props.directoryId) {
      resetForm()
      return
    }

    try {
      // 从store获取目录详情
      const directory = await directoryStore.getDirectory(props.directoryId)

      console.log('加载目录数据:', directory)
      if (directory) {
        formData.name = directory.name || ''
        formData.visibility = directory.visibility || 'public'
        formData.sortOrder = directory.sort_order || 0
      } else {
        // 如果无法获取详情，至少设置名称
        formData.name = props.directoryName || ''
        formData.visibility = 'public'
        formData.sortOrder = 0
      }
    } catch (error) {
      console.error('加载目录数据失败:', error)
      // 降级处理，使用传入的名称
      formData.name = props.directoryName || ''
      formData.visibility = 'public'
      formData.sortOrder = 0
    }
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      isSubmitting.value = true

      // 准备目标ID列表
      let targetIds = []
      if (formData.visibility === 'user') {
        targetIds = formData.selectedUsers.map(user => user.id)
      } else if (formData.visibility === 'department') {
        targetIds = formData.selectedDepartments
      } else if (formData.visibility === 'private') {
        targetIds = [user_id.value]
      }

      if (props.isEditMode) {
        // 编辑模式：更新目录
        const updateData = {
          name: formData.name.trim(),
          visibility: formData.visibility,
          sort_order: formData.sortOrder,
          target_type: formData.targetType,
          target_ids: targetIds,
        }

        const updatedDirectory = await directoryStore.updateDirectory(
          props.directoryId,
          updateData
        )

        $baseMessage('目录设置更新成功', 'success', 'vab-hey-message-success')

        emit('success', updatedDirectory)
      } else {
        // 创建模式：创建新目录
        const createData = {
          parent_id: props.parentId,
          name: formData.name.trim(),
          visibility: formData.visibility,
          sort_order: formData.sortOrder,
          target_type: formData.targetType,
          target_ids: targetIds,
        }

        const newDirectory = await directoryStore.createDirectory(createData)

        $baseMessage('目录创建成功', 'success', 'vab-hey-message-success')

        emit('success', newDirectory)
      }

      handleClose()
    } catch (error) {
      console.error(error)
    } finally {
      isSubmitting.value = false
    }
  }

  const handleClose = () => {
    dialogVisible.value = false
  }

  // 表单验证方法
  const validateName = (rule, value, callback) => {
    if (!value || !value.trim()) {
      callback(new Error('请输入目录名称'))
      return
    }

    const trimmedValue = value.trim()

    if (trimmedValue.length < 1 || trimmedValue.length > 100) {
      callback(new Error('目录名称长度在 1 到 100 个字符'))
      return
    }

    // 检查特殊字符
    const invalidChars = /[<>:"/\\|?*]/
    if (invalidChars.test(trimmedValue)) {
      callback(new Error('目录名称不能包含特殊字符 < > : " / \\ | ? *'))
      return
    }

    // 检查是否以点开头或结尾
    if (trimmedValue.startsWith('.') || trimmedValue.endsWith('.')) {
      callback(new Error('目录名称不能以点开头或结尾'))
      return
    }

    callback()
  }

  // 更新验证规则
  formRules.name = [
    { required: true, validator: validateName, trigger: 'blur' },
  ]
</script>

<style lang="scss" scoped>
  .create-directory-dialog {
    &__visibility-help {
      margin-top: 4px;
    }

    &__footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }

    &__user-item {
      display: inline-flex;
      align-items: center;
      padding: 4px 8px;
      margin: 2px 4px 2px 0;
      background-color: var(--el-color-primary-light-9);
      border: 1px solid var(--el-color-primary-light-7);
      border-radius: 4px;
      font-size: 12px;
      color: var(--el-color-primary);
      gap: 4px;

      .vab-icon {
        cursor: pointer;
        font-size: 14px;
        color: var(--el-color-primary-light-3);
        transition: color 0.2s;

        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  :deep(.el-radio) {
    display: flex;
    align-items: center;
    margin-right: 24px;
    margin-bottom: 8px;

    .el-radio__label {
      display: flex;
      align-items: center;
      gap: 4px;
      padding-left: 8px;
    }
  }

  :deep(.el-input-number) {
    .el-input__inner {
      text-align: left;
    }
  }
</style>
