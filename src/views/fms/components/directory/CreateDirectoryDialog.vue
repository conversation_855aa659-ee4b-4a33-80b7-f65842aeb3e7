<template>
  <firefly-dialog
    v-model="dialogVisible"
    title="新建目录"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="父目录" prop="parentPath">
        <el-input
          v-model="parentPath"
          readonly
          placeholder="根目录"
        >
          <template #prefix>
            <el-icon><Folder /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      
      <el-form-item label="目录名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入目录名称"
          maxlength="100"
          show-word-limit
          @keyup.enter="handleSubmit"
        >
          <template #prefix>
            <el-icon><FolderAdd /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      
      <el-form-item label="可见性" prop="visibility">
        <el-radio-group v-model="formData.visibility">
          <el-radio label="public">
            <el-icon><View /></el-icon>
            公开
          </el-radio>
          <el-radio label="private">
            <el-icon><Hide /></el-icon>
            私有
          </el-radio>
          <el-radio label="internal">
            <el-icon><User /></el-icon>
            内部
          </el-radio>
        </el-radio-group>
        <div class="create-directory-dialog__visibility-help">
          <el-text size="small" type="info">
            公开：所有人可见；私有：仅创建者可见；内部：组织内部可见
          </el-text>
        </div>
      </el-form-item>
      
      <el-form-item label="排序" prop="sortOrder">
        <el-input-number
          v-model="formData.sortOrder"
          :min="0"
          :max="9999"
          placeholder="排序值"
          style="width: 150px"
        />
        <el-text size="small" type="info" style="margin-left: 12px">
          数值越小排序越靠前
        </el-text>
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入目录描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="create-directory-dialog__footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="isSubmitting"
          @click="handleSubmit"
        >
          创建目录
        </el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, inject, defineProps, defineEmits } from 'vue'
import {
  Folder,
  FolderAdd,
  View,
  Hide,
  User
} from '@element-plus/icons-vue'
import { useDirectoryStore } from '../../stores/directoryStore'

// Props 定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  parentId: {
    type: [Number, String],
    default: null
  }
})

// Events 定义
const emit = defineEmits(['update:visible', 'success'])

// 注入服务
const $baseMessage = inject('$baseMessage')

// Store
const directoryStore = useDirectoryStore()

// 响应式数据
const formRef = ref()
const isSubmitting = ref(false)

const formData = reactive({
  name: '',
  visibility: 'public',
  sortOrder: 0,
  description: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入目录名称', trigger: 'blur' },
    { min: 1, max: 100, message: '目录名称长度在 1 到 100 个字符', trigger: 'blur' },
    {
      pattern: /^[^<>:"/\\|?*]+$/,
      message: '目录名称不能包含特殊字符 < > : " / \\ | ? *',
      trigger: 'blur'
    }
  ],
  visibility: [
    { required: true, message: '请选择可见性', trigger: 'change' }
  ],
  sortOrder: [
    { type: 'number', min: 0, max: 9999, message: '排序值范围为 0-9999', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const parentPath = computed(() => {
  if (!props.parentId) return '根目录'
  
  // TODO: 从store获取父目录路径
  const parentDirectory = directoryStore.directoryMap.get(props.parentId)
  return parentDirectory ? parentDirectory.path : '未知目录'
})

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    resetForm()
  }
})

// 方法
const resetForm = () => {
  formData.name = ''
  formData.visibility = 'public'
  formData.sortOrder = 0
  formData.description = ''
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    isSubmitting.value = true
    
    const createData = {
      parentId: props.parentId,
      name: formData.name.trim(),
      visibility: formData.visibility,
      sortOrder: formData.sortOrder
    }
    
    // 如果有描述，添加到元数据中
    if (formData.description.trim()) {
      createData.metadata = {
        description: formData.description.trim()
      }
    }
    
    const newDirectory = await directoryStore.createDirectory(createData)
    
    $baseMessage('目录创建成功', 'success', 'vab-hey-message-success')
    
    emit('success', newDirectory)
    handleClose()
    
  } catch (error) {
    $baseMessage(error.message || '目录创建失败', 'error', 'vab-hey-message-error')
  } finally {
    isSubmitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

// 表单验证方法
const validateName = (rule, value, callback) => {
  if (!value || !value.trim()) {
    callback(new Error('请输入目录名称'))
    return
  }
  
  const trimmedValue = value.trim()
  
  if (trimmedValue.length < 1 || trimmedValue.length > 100) {
    callback(new Error('目录名称长度在 1 到 100 个字符'))
    return
  }
  
  // 检查特殊字符
  const invalidChars = /[<>:"/\\|?*]/
  if (invalidChars.test(trimmedValue)) {
    callback(new Error('目录名称不能包含特殊字符 < > : " / \\ | ? *'))
    return
  }
  
  // 检查是否以点开头或结尾
  if (trimmedValue.startsWith('.') || trimmedValue.endsWith('.')) {
    callback(new Error('目录名称不能以点开头或结尾'))
    return
  }
  
  callback()
}

// 更新验证规则
formRules.name = [
  { required: true, validator: validateName, trigger: 'blur' }
]
</script>

<style lang="scss" scoped>
.create-directory-dialog {
  &__visibility-help {
    margin-top: 4px;
  }
  
  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

:deep(.el-radio) {
  display: flex;
  align-items: center;
  margin-right: 24px;
  margin-bottom: 8px;
  
  .el-radio__label {
    display: flex;
    align-items: center;
    gap: 4px;
    padding-left: 8px;
  }
}

:deep(.el-input-number) {
  .el-input__inner {
    text-align: left;
  }
}
</style>
