<template>
  <firefly-dialog
    v-model="dialogVisible"
    title="上传文件"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="file-upload">
      <div class="file-upload__dropzone">
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :file-list="fileList"
          :auto-upload="false"
          :show-file-list="false"
          :accept="acceptTypes"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :on-progress="handleProgress"
          :on-success="handleSuccess"
          :on-error="handleError"
          multiple
          drag
          class="file-upload__uploader"
        >
          <div class="file-upload__drop-area">
            <el-icon class="file-upload__drop-icon">
              <UploadFilled />
            </el-icon>
            <div class="file-upload__drop-text">
              <div class="file-upload__drop-title">拖拽文件到此处或点击上传</div>
              <div class="file-upload__drop-hint">
                支持多文件上传，单个文件最大 {{ maxSizeText }}
              </div>
            </div>
          </div>
        </el-upload>
      </div>
      
      <div v-if="fileList.length > 0" class="file-upload__list">
        <div class="file-upload__list-header">
          <span>待上传文件 ({{ fileList.length }})</span>
          <el-button
            type="text"
            size="small"
            @click="clearFiles"
          >
            清空列表
          </el-button>
        </div>
        
        <div class="file-upload__list-content">
          <div
            v-for="(file, index) in fileList"
            :key="file.uid"
            class="file-upload__item"
          >
            <div class="file-upload__item-icon">
              <el-icon>
                <component :is="getFileIcon(file)" />
              </el-icon>
            </div>
            
            <div class="file-upload__item-info">
              <div class="file-upload__item-name">{{ file.name }}</div>
              <div class="file-upload__item-meta">
                <span>{{ formatFileSize(file.size) }}</span>
                <span v-if="file.status === 'uploading'">
                  {{ file.percentage }}%
                </span>
                <span v-else-if="file.status === 'success'" class="success">
                  上传成功
                </span>
                <span v-else-if="file.status === 'error'" class="error">
                  上传失败
                </span>
              </div>
            </div>
            
            <div class="file-upload__item-progress">
              <el-progress
                v-if="file.status === 'uploading'"
                :percentage="file.percentage"
                :stroke-width="4"
                :show-text="false"
              />
            </div>
            
            <div class="file-upload__item-actions">
              <el-button
                v-if="file.status === 'ready'"
                type="text"
                size="small"
                @click="removeFile(index)"
              >
                <el-icon><Close /></el-icon>
              </el-button>
              <el-button
                v-else-if="file.status === 'error'"
                type="text"
                size="small"
                @click="retryUpload(file)"
              >
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="file-upload__options">
        <el-form :model="uploadOptions" label-width="80px">
          <el-form-item label="可见性">
            <el-radio-group v-model="uploadOptions.visibility" size="small">
              <el-radio label="public">公开</el-radio>
              <el-radio label="private">私有</el-radio>
              <el-radio label="internal">内部</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="标签">
            <el-select
              v-model="uploadOptions.tagIds"
              placeholder="选择标签（可选）"
              multiple
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="tag in availableTags"
                :key="tag.id"
                :label="tag.name"
                :value="tag.id"
              >
                <span :style="{ color: tag.color }">{{ tag.name }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="描述">
            <el-input
              v-model="uploadOptions.description"
              type="textarea"
              :rows="2"
              placeholder="文件描述（可选）"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
    
    <template #footer>
      <div class="file-upload__footer">
        <div class="file-upload__footer-info">
          <span v-if="uploadStats.total > 0">
            {{ uploadStats.success }}/{{ uploadStats.total }} 已完成
          </span>
        </div>
        <div class="file-upload__footer-actions">
          <el-button @click="handleClose">
            取消
          </el-button>
          <el-button
            type="primary"
            :loading="isUploading"
            :disabled="fileList.length === 0"
            @click="startUpload"
          >
            开始上传
          </el-button>
        </div>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, inject, defineProps, defineEmits } from 'vue'
import {
  UploadFilled,
  Close,
  Refresh,
  Document,
  Picture,
  VideoCamera,
  Headset,
  Files
} from '@element-plus/icons-vue'
import { useFileStore } from '../../stores/fileStore'
import { useFmsStore } from '../../stores/fmsStore'
import { useTagStore } from '../../stores/tagStore'

// Props 定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  directoryId: {
    type: [Number, String],
    default: null
  },
  maxSize: {
    type: Number,
    default: 100 * 1024 * 1024 // 100MB
  },
  acceptTypes: {
    type: String,
    default: ''
  }
})

// Events 定义
const emit = defineEmits(['update:visible', 'success'])

// 注入服务
const $baseMessage = inject('$baseMessage')

// Store
const fileStore = useFileStore()
const fmsStore = useFmsStore()
const tagStore = useTagStore()

// 响应式数据
const uploadRef = ref()
const fileList = ref([])
const isUploading = ref(false)
const availableTags = ref([])

const uploadOptions = reactive({
  visibility: 'public',
  tagIds: [],
  description: ''
})

const uploadStats = reactive({
  total: 0,
  success: 0,
  error: 0
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const maxSizeText = computed(() => {
  return fmsStore.formatFileSize(props.maxSize)
})

const uploadAction = computed(() => {
  return `/api/fms/files/upload`
})

const uploadHeaders = computed(() => {
  return {
    'Authorization': `Bearer ${fmsStore.token}`,
    'X-Directory-Id': props.directoryId
  }
})

const uploadData = computed(() => {
  return {
    directoryId: props.directoryId,
    visibility: uploadOptions.visibility,
    tagIds: uploadOptions.tagIds.join(','),
    description: uploadOptions.description
  }
})

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    resetUpload()
    loadTags()
  }
})

// 方法
const resetUpload = () => {
  fileList.value = []
  isUploading.value = false
  uploadOptions.visibility = 'public'
  uploadOptions.tagIds = []
  uploadOptions.description = ''
  uploadStats.total = 0
  uploadStats.success = 0
  uploadStats.error = 0
}

const loadTags = async () => {
  try {
    const response = await tagStore.fetchTags()
    availableTags.value = response.data || []
  } catch (error) {
    console.warn('加载标签失败:', error)
  }
}

const beforeUpload = (file) => {
  // 检查文件大小
  if (file.size > props.maxSize) {
    $baseMessage(
      `文件 "${file.name}" 超过大小限制 ${maxSizeText.value}`,
      'error',
      'vab-hey-message-error'
    )
    return false
  }
  
  // 检查文件类型
  if (props.acceptTypes && !isAcceptedType(file)) {
    $baseMessage(
      `文件 "${file.name}" 类型不支持`,
      'error',
      'vab-hey-message-error'
    )
    return false
  }
  
  return true
}

const isAcceptedType = (file) => {
  if (!props.acceptTypes) return true
  
  const acceptedTypes = props.acceptTypes.split(',').map(type => type.trim())
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
  const fileMimeType = file.type
  
  return acceptedTypes.some(type => {
    if (type.startsWith('.')) {
      return fileExtension === type.toLowerCase()
    } else {
      return fileMimeType.startsWith(type.replace('*', ''))
    }
  })
}

const handleFileChange = (file, files) => {
  // 更新文件列表
  fileList.value = files.map(f => ({
    ...f,
    status: 'ready',
    percentage: 0
  }))
}

const handleProgress = (event, file) => {
  const targetFile = fileList.value.find(f => f.uid === file.uid)
  if (targetFile) {
    targetFile.status = 'uploading'
    targetFile.percentage = Math.round(event.percent)
  }
}

const handleSuccess = (response, file) => {
  const targetFile = fileList.value.find(f => f.uid === file.uid)
  if (targetFile) {
    targetFile.status = 'success'
    targetFile.percentage = 100
    uploadStats.success++
  }
  
  checkUploadComplete()
}

const handleError = (error, file) => {
  const targetFile = fileList.value.find(f => f.uid === file.uid)
  if (targetFile) {
    targetFile.status = 'error'
    targetFile.percentage = 0
    uploadStats.error++
  }
  
  $baseMessage(`文件 "${file.name}" 上传失败`, 'error', 'vab-hey-message-error')
  checkUploadComplete()
}

const startUpload = () => {
  if (fileList.value.length === 0) return
  
  isUploading.value = true
  uploadStats.total = fileList.value.length
  uploadStats.success = 0
  uploadStats.error = 0
  
  // 开始上传
  uploadRef.value.submit()
}

const checkUploadComplete = () => {
  if (uploadStats.success + uploadStats.error >= uploadStats.total) {
    isUploading.value = false
    
    if (uploadStats.success > 0) {
      $baseMessage(
        `成功上传 ${uploadStats.success} 个文件`,
        'success',
        'vab-hey-message-success'
      )
      
      emit('success', {
        total: uploadStats.total,
        success: uploadStats.success,
        error: uploadStats.error
      })
    }
    
    if (uploadStats.error === 0) {
      // 全部成功，关闭对话框
      setTimeout(() => {
        handleClose()
      }, 1000)
    }
  }
}

const removeFile = (index) => {
  fileList.value.splice(index, 1)
}

const retryUpload = (file) => {
  file.status = 'ready'
  file.percentage = 0
  // TODO: 实现单个文件重试上传
}

const clearFiles = () => {
  fileList.value = []
}

const handleClose = () => {
  if (isUploading.value) {
    $baseMessage('上传进行中，无法关闭', 'warning', 'vab-hey-message-warning')
    return
  }
  
  dialogVisible.value = false
}

// 工具方法
const formatFileSize = (bytes) => {
  return fmsStore.formatFileSize(bytes)
}

const getFileIcon = (file) => {
  return fmsStore.getFileIcon(file)
}

// 生命周期
onMounted(() => {
  if (props.visible) {
    loadTags()
  }
})
</script>

<style lang="scss" scoped>
.file-upload {
  &__dropzone {
    margin-bottom: 20px;
  }
  
  &__uploader {
    :deep(.el-upload) {
      width: 100%;
    }
    
    :deep(.el-upload-dragger) {
      width: 100%;
      height: 120px;
      border: 2px dashed var(--el-border-color);
      border-radius: 8px;
      background: var(--el-fill-color-extra-light);
      transition: all 0.3s;
      
      &:hover {
        border-color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
      }
    }
  }
  
  &__drop-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20px;
  }
  
  &__drop-icon {
    font-size: 32px;
    color: var(--el-color-primary);
    margin-bottom: 12px;
  }
  
  &__drop-text {
    text-align: center;
  }
  
  &__drop-title {
    font-size: 16px;
    color: var(--el-text-color-primary);
    margin-bottom: 4px;
  }
  
  &__drop-hint {
    font-size: 13px;
    color: var(--el-text-color-regular);
  }
  
  &__list {
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    margin-bottom: 20px;
    
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: var(--el-bg-color-page);
      border-bottom: 1px solid var(--el-border-color-lighter);
      font-weight: 500;
    }
    
    &-content {
      max-height: 200px;
      overflow-y: auto;
    }
  }
  
  &__item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    &:last-child {
      border-bottom: none;
    }
    
    &-icon {
      margin-right: 12px;
      
      .el-icon {
        font-size: 20px;
        color: var(--el-color-primary);
      }
    }
    
    &-info {
      flex: 1;
      min-width: 0;
    }
    
    &-name {
      font-weight: 500;
      color: var(--el-text-color-primary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    &-meta {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-top: 2px;
      
      span {
        margin-right: 12px;
        
        &.success {
          color: var(--el-color-success);
        }
        
        &.error {
          color: var(--el-color-error);
        }
      }
    }
    
    &-progress {
      width: 100px;
      margin: 0 12px;
    }
    
    &-actions {
      flex-shrink: 0;
    }
  }
  
  &__options {
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    padding: 16px;
    background: var(--el-bg-color-page);
  }
  
  &__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    &-info {
      font-size: 13px;
      color: var(--el-text-color-regular);
    }
    
    &-actions {
      display: flex;
      gap: 12px;
    }
  }
}
</style>
