<template>
  <firefly-dialog
    v-model="dialogVisible"
    :title="`编辑文件信息 - ${file?.name || ''}`"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="file-meta-editor">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="文件名" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入文件名"
            maxlength="255"
            show-word-limit
          >
            <template #prefix>
              <el-icon><Document /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="可见性" prop="visibility">
          <el-radio-group v-model="formData.visibility">
            <el-radio label="public">
              <el-icon><View /></el-icon>
              公开
            </el-radio>
            <el-radio label="private">
              <el-icon><Hide /></el-icon>
              私有
            </el-radio>
            <el-radio label="internal">
              <el-icon><User /></el-icon>
              内部
            </el-radio>
          </el-radio-group>
          <div class="file-meta-editor__help-text">
            <el-text size="small" type="info">
              公开：所有人可见；私有：仅创建者可见；内部：组织内部可见
            </el-text>
          </div>
        </el-form-item>
        
        <el-form-item label="标签" prop="tagIds">
          <el-select
            v-model="formData.tagIds"
            placeholder="选择标签"
            multiple
            filterable
            allow-create
            default-first-option
            style="width: 100%"
            @change="handleTagChange"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag.id"
              :label="tag.name"
              :value="tag.id"
            >
              <div class="file-meta-editor__tag-option">
                <span
                  class="file-meta-editor__tag-color"
                  :style="{ backgroundColor: tag.color }"
                />
                <span>{{ tag.name }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="file-meta-editor__selected-tags">
            <el-tag
              v-for="tagId in formData.tagIds"
              :key="tagId"
              :color="getTagColor(tagId)"
              size="small"
              closable
              @close="removeTag(tagId)"
            >
              {{ getTagName(tagId) }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            placeholder="请输入文件描述"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="自定义属性">
          <div class="file-meta-editor__custom-fields">
            <div
              v-for="(field, index) in formData.customFields"
              :key="index"
              class="file-meta-editor__custom-field"
            >
              <el-input
                v-model="field.key"
                placeholder="属性名"
                style="width: 150px"
              />
              <el-input
                v-model="field.value"
                placeholder="属性值"
                style="flex: 1; margin: 0 8px"
              />
              <el-button
                type="text"
                :icon="Delete"
                @click="removeCustomField(index)"
              />
            </div>
            <el-button
              type="text"
              :icon="Plus"
              @click="addCustomField"
            >
              添加自定义属性
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number
            v-model="formData.sortOrder"
            :min="0"
            :max="9999"
            placeholder="排序值"
            style="width: 150px"
          />
          <el-text size="small" type="info" style="margin-left: 12px">
            数值越小排序越靠前
          </el-text>
        </el-form-item>
      </el-form>
      
      <div class="file-meta-editor__info">
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="文件大小">
            {{ formatFileSize(file?.size) }}
          </el-descriptions-item>
          <el-descriptions-item label="文件类型">
            {{ file?.mimeType }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(file?.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="修改时间">
            {{ formatDateTime(file?.updatedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建者">
            {{ file?.creator?.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="版本">
            v{{ file?.version || '1.0' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    
    <template #footer>
      <div class="file-meta-editor__footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="isSubmitting"
          @click="handleSubmit"
        >
          保存修改
        </el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, inject, defineProps, defineEmits } from 'vue'
import {
  Document,
  View,
  Hide,
  User,
  Plus,
  Delete
} from '@element-plus/icons-vue'
import { useFileStore } from '../../stores/fileStore'
import { useFmsStore } from '../../stores/fmsStore'
import { useTagStore } from '../../stores/tagStore'

// Props 定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  file: {
    type: Object,
    default: null
  }
})

// Events 定义
const emit = defineEmits(['update:visible', 'success'])

// 注入服务
const $baseMessage = inject('$baseMessage')

// Store
const fileStore = useFileStore()
const fmsStore = useFmsStore()
const tagStore = useTagStore()

// 响应式数据
const formRef = ref()
const isSubmitting = ref(false)
const availableTags = ref([])

const formData = reactive({
  name: '',
  visibility: 'public',
  tagIds: [],
  description: '',
  customFields: [],
  sortOrder: 0
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入文件名', trigger: 'blur' },
    { min: 1, max: 255, message: '文件名长度在 1 到 255 个字符', trigger: 'blur' }
  ],
  visibility: [
    { required: true, message: '请选择可见性', trigger: 'change' }
  ],
  sortOrder: [
    { type: 'number', min: 0, max: 9999, message: '排序值范围为 0-9999', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible && props.file) {
    resetForm()
    loadTags()
  }
})

// 监听文件变化
watch(() => props.file, (file) => {
  if (file && props.visible) {
    resetForm()
  }
})

// 方法
const resetForm = () => {
  if (!props.file) return
  
  formData.name = props.file.name || ''
  formData.visibility = props.file.visibility || 'public'
  formData.tagIds = props.file.tags?.map(tag => tag.id) || []
  formData.description = props.file.description || ''
  formData.sortOrder = props.file.sortOrder || 0
  
  // 处理自定义字段
  formData.customFields = []
  if (props.file.metadata && typeof props.file.metadata === 'object') {
    Object.entries(props.file.metadata).forEach(([key, value]) => {
      if (key !== 'description') { // 描述单独处理
        formData.customFields.push({ key, value: String(value) })
      }
    })
  }
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const loadTags = async () => {
  try {
    const response = await tagStore.fetchTags()
    availableTags.value = response.data || []
  } catch (error) {
    console.warn('加载标签失败:', error)
  }
}

const handleTagChange = (tagIds) => {
  // 处理新创建的标签
  const newTags = tagIds.filter(id => typeof id === 'string' && !availableTags.value.find(tag => tag.id === id))
  
  if (newTags.length > 0) {
    // TODO: 创建新标签
    $baseMessage('新标签创建功能开发中', 'info', 'vab-hey-message-info')
  }
}

const addCustomField = () => {
  formData.customFields.push({ key: '', value: '' })
}

const removeCustomField = (index) => {
  formData.customFields.splice(index, 1)
}

const removeTag = (tagId) => {
  const index = formData.tagIds.indexOf(tagId)
  if (index > -1) {
    formData.tagIds.splice(index, 1)
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    isSubmitting.value = true
    
    // 构建更新数据
    const updateData = {
      name: formData.name.trim(),
      visibility: formData.visibility,
      tagIds: formData.tagIds,
      description: formData.description.trim(),
      sortOrder: formData.sortOrder
    }
    
    // 处理自定义字段
    const metadata = {}
    if (formData.description.trim()) {
      metadata.description = formData.description.trim()
    }
    
    formData.customFields.forEach(field => {
      if (field.key.trim() && field.value.trim()) {
        metadata[field.key.trim()] = field.value.trim()
      }
    })
    
    if (Object.keys(metadata).length > 0) {
      updateData.metadata = metadata
    }
    
    await fileStore.updateFile(props.file.id, updateData)
    
    $baseMessage('文件信息更新成功', 'success', 'vab-hey-message-success')
    
    emit('success', {
      id: props.file.id,
      ...updateData
    })
    
    handleClose()
    
  } catch (error) {
    $baseMessage(error.message || '文件信息更新失败', 'error', 'vab-hey-message-error')
  } finally {
    isSubmitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

// 工具方法
const formatFileSize = (bytes) => {
  return fmsStore.formatFileSize(bytes)
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getTagName = (tagId) => {
  const tag = availableTags.value.find(t => t.id === tagId)
  return tag ? tag.name : String(tagId)
}

const getTagColor = (tagId) => {
  const tag = availableTags.value.find(t => t.id === tagId)
  return tag ? tag.color : '#409eff'
}

// 生命周期
onMounted(() => {
  if (props.visible && props.file) {
    resetForm()
    loadTags()
  }
})
</script>

<style lang="scss" scoped>
.file-meta-editor {
  &__help-text {
    margin-top: 4px;
  }
  
  &__tag-option {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  &__tag-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
  }
  
  &__selected-tags {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  &__custom-fields {
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    padding: 12px;
    background: var(--el-bg-color-page);
  }
  
  &__custom-field {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  &__info {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--el-border-color-light);
  }
  
  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

:deep(.el-radio) {
  display: flex;
  align-items: center;
  margin-right: 24px;
  margin-bottom: 8px;
  
  .el-radio__label {
    display: flex;
    align-items: center;
    gap: 4px;
    padding-left: 8px;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 500;
  }
}
</style>
