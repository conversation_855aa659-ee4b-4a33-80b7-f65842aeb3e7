<template>
  <firefly-dialog
    v-model="dialogVisible"
    :title="`版本历史 - ${file?.name || ''}`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="file-versions">
      <div class="file-versions__header">
        <div class="file-versions__info">
          <div class="file-versions__current">
            <span class="file-versions__label">当前版本：</span>
            <span class="file-versions__version">
              v{{ currentVersion?.version || '1.0' }}
            </span>
            <span class="file-versions__size">
              {{ formatFileSize(file?.size) }}
            </span>
          </div>
          <div class="file-versions__total">
            共 {{ versions.length }} 个版本
          </div>
        </div>
        <div class="file-versions__actions">
          <el-button
            type="primary"
            :icon="Upload"
            @click="handleUploadVersion"
            v-fms-permission="{
              targetType: 'file',
              targetId: file?.id,
              permission: FMS_PERMISSIONS.UPLOAD,
            }"
          >
            上传新版本
          </el-button>
        </div>
      </div>

      <div class="file-versions__list" v-loading="isLoading">
        <div
          v-for="version in versions"
          :key="version.id"
          class="file-versions__item"
          :class="{ 'is-current': version.isCurrent }"
        >
          <div class="file-versions__item-header">
            <div class="file-versions__item-info">
              <div class="file-versions__item-version">
                <span class="file-versions__version-number">
                  v{{ version.version }}
                </span>
                <el-tag v-if="version.isCurrent" type="success" size="small">
                  当前版本
                </el-tag>
                <el-tag
                  v-else-if="version.status === 'draft'"
                  type="warning"
                  size="small"
                >
                  草稿
                </el-tag>
              </div>
              <div class="file-versions__item-meta">
                <span>{{ formatFileSize(version.size) }}</span>
                <span>{{ formatDateTime(version.createdAt) }}</span>
                <span v-if="version.creator">{{ version.creator.name }}</span>
              </div>
            </div>
            <div class="file-versions__item-actions">
              <el-button
                type="text"
                size="small"
                @click="handlePreview(version)"
                v-if="canPreview(version)"
              >
                预览
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleDownload(version)"
              >
                下载
              </el-button>
              <el-dropdown
                v-if="!version.isCurrent"
                @command="(command) => handleCommand(command, version)"
              >
                <el-button type="text" size="small">
                  更多
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      command="restore"
                      v-fms-permission="{
                        targetType: 'file',
                        targetId: file?.id,
                        permission: FMS_PERMISSIONS.MANAGE,
                      }"
                    >
                      恢复此版本
                    </el-dropdown-item>
                    <el-dropdown-item command="compare">
                      与当前版本对比
                    </el-dropdown-item>
                    <el-dropdown-item
                      command="delete"
                      divided
                      v-fms-permission="{
                        targetType: 'file',
                        targetId: file?.id,
                        permission: FMS_PERMISSIONS.DELETE,
                      }"
                    >
                      删除版本
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div v-if="version.comment" class="file-versions__item-comment">
            <div class="file-versions__comment-label">版本说明：</div>
            <div class="file-versions__comment-content">
              {{ version.comment }}
            </div>
          </div>

          <div
            v-if="version.changes?.length > 0"
            class="file-versions__item-changes"
          >
            <div class="file-versions__changes-label">主要变更：</div>
            <ul class="file-versions__changes-list">
              <li
                v-for="change in version.changes"
                :key="change.id"
                class="file-versions__change-item"
              >
                <el-icon :class="`file-versions__change-icon--${change.type}`">
                  <component :is="getChangeIcon(change.type)" />
                </el-icon>
                <span>{{ change.description }}</span>
              </li>
            </ul>
          </div>
        </div>

        <div
          v-if="!isLoading && versions.length === 0"
          class="file-versions__empty"
        >
          <el-empty description="暂无版本历史" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="file-versions__footer">
        <div class="file-versions__footer-info">
          <el-text size="small" type="info">
            版本保留策略：最多保留 50 个版本，超出部分将自动清理
          </el-text>
        </div>
        <div class="file-versions__footer-actions">
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </template>

    <!-- 上传新版本对话框 -->
    <upload-version-dialog
      v-model:visible="showUploadDialog"
      :file="file"
      @success="handleUploadSuccess"
    />

    <!-- 版本对比对话框 -->
    <version-compare-dialog
      v-model:visible="showCompareDialog"
      :file="file"
      :version1="compareVersion1"
      :version2="compareVersion2"
    />
  </firefly-dialog>
</template>

<script setup>
  import {
    ref,
    computed,
    watch,
    onMounted,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import {
    Upload,
    ArrowDown,
    Plus,
    Edit,
    Delete,
    Warning,
  } from '@element-plus/icons-vue'
  import { useFileStore } from '../../stores/fileStore'
  import { useFmsStore } from '../../stores/fmsStore'
  import { useFmsPermission, vFmsPermission } from '@/utils/bmsPermission'
  import UploadVersionDialog from './UploadVersionDialog.vue'
  import VersionCompareDialog from './VersionCompareDialog.vue'

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    file: {
      type: Object,
      default: null,
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible', 'version-restored'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // Store
  const fileStore = useFileStore()
  const fmsStore = useFmsStore()
  const { FMS_PERMISSIONS } = useFmsPermission()

  // 响应式数据
  const isLoading = ref(false)
  const versions = ref([])
  const showUploadDialog = ref(false)
  const showCompareDialog = ref(false)
  const compareVersion1 = ref(null)
  const compareVersion2 = ref(null)

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  const currentVersion = computed(() => {
    return versions.value.find((v) => v.isCurrent)
  })

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible && props.file) {
        loadVersions()
      }
    }
  )

  // 监听文件变化
  watch(
    () => props.file,
    (file) => {
      if (file && props.visible) {
        loadVersions()
      }
    }
  )

  // 方法
  const loadVersions = async () => {
    if (!props.file) return

    try {
      isLoading.value = true
      const response = await fileStore.fetchFileVersions(props.file.id)
      versions.value = response.data || []
    } catch (error) {
      $baseMessage('加载版本历史失败', 'error', 'vab-hey-message-error')
    } finally {
      isLoading.value = false
    }
  }

  const handleUploadVersion = () => {
    showUploadDialog.value = true
  }

  const handleUploadSuccess = () => {
    loadVersions()
    $baseMessage('新版本上传成功', 'success', 'vab-hey-message-success')
  }

  const handlePreview = (version) => {
    // TODO: 实现版本预览
    $baseMessage('版本预览功能开发中', 'info', 'vab-hey-message-info')
  }

  const handleDownload = async (version) => {
    try {
      await fileStore.downloadFileVersion(props.file.id, version.id)
      $baseMessage('版本下载开始', 'success', 'vab-hey-message-success')
    } catch (error) {
      $baseMessage('版本下载失败', 'error', 'vab-hey-message-error')
    }
  }

  const handleCommand = (command, version) => {
    switch (command) {
      case 'restore':
        handleRestore(version)
        break
      case 'compare':
        handleCompare(version)
        break
      case 'delete':
        handleDelete(version)
        break
    }
  }

  const handleRestore = (version) => {
    $baseConfirm(
      `确认将文件恢复到版本 v${version.version}？这将创建一个新的版本。`,
      () => {},
      async () => {
        try {
          await fileStore.restoreFileVersion(props.file.id, version.id)
          $baseMessage('版本恢复成功', 'success', 'vab-hey-message-success')

          emit('version-restored', {
            fileId: props.file.id,
            versionId: version.id,
            version: version.version,
          })

          await loadVersions()
        } catch (error) {
          $baseMessage('版本恢复失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handleCompare = (version) => {
    compareVersion1.value = currentVersion.value
    compareVersion2.value = version
    showCompareDialog.value = true
  }

  const handleDelete = (version) => {
    $baseConfirm(
      `确认删除版本 v${version.version}？此操作不可恢复。`,
      () => {},
      async () => {
        try {
          await fileStore.deleteFileVersion(props.file.id, version.id)
          $baseMessage('版本删除成功', 'success', 'vab-hey-message-success')
          await loadVersions()
        } catch (error) {
          $baseMessage('版本删除失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handleClose = () => {
    dialogVisible.value = false
  }

  // 工具方法
  const formatFileSize = (bytes) => {
    return fmsStore.formatFileSize(bytes)
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  const canPreview = (version) => {
    return fmsStore.canPreviewFile({ ...props.file, ...version })
  }

  const getChangeIcon = (type) => {
    const iconMap = {
      add: Plus,
      edit: Edit,
      delete: Delete,
      warning: Warning,
    }
    return iconMap[type] || Edit
  }

  // 生命周期
  onMounted(() => {
    if (props.visible && props.file) {
      loadVersions()
    }
  })
</script>

<style lang="scss" scoped>
  .file-versions {
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0;
      border-bottom: 1px solid var(--el-border-color-light);
    }

    &__info {
      flex: 1;
    }

    &__current {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
    }

    &__label {
      color: var(--el-text-color-regular);
    }

    &__version {
      color: var(--el-color-primary);
      margin: 0 8px;
    }

    &__size {
      color: var(--el-text-color-regular);
      font-size: 14px;
    }

    &__total {
      font-size: 13px;
      color: var(--el-text-color-regular);
    }

    &__list {
      max-height: 500px;
      overflow-y: auto;
      margin: 16px 0;
    }

    &__item {
      padding: 16px;
      border: 1px solid var(--el-border-color-light);
      border-radius: 6px;
      margin-bottom: 12px;
      transition: all 0.2s;

      &:hover {
        border-color: var(--el-color-primary-light-7);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &.is-current {
        border-color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
      }

      &:last-child {
        margin-bottom: 0;
      }

      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      &-info {
        flex: 1;
      }

      &-version {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
      }

      &-meta {
        font-size: 13px;
        color: var(--el-text-color-regular);

        span {
          margin-right: 16px;
        }
      }

      &-actions {
        display: flex;
        gap: 4px;
      }

      &-comment {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid var(--el-border-color-lighter);
      }

      &-changes {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid var(--el-border-color-lighter);
      }
    }

    &__version-number {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    &__comment-label,
    &__changes-label {
      font-size: 13px;
      font-weight: 500;
      color: var(--el-text-color-regular);
      margin-bottom: 6px;
    }

    &__comment-content {
      font-size: 14px;
      color: var(--el-text-color-primary);
      line-height: 1.5;
    }

    &__changes-list {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    &__change-item {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 4px;
      font-size: 13px;
      color: var(--el-text-color-primary);

      &:last-child {
        margin-bottom: 0;
      }
    }

    &__change-icon {
      font-size: 12px;

      &--add {
        color: var(--el-color-success);
      }

      &--edit {
        color: var(--el-color-primary);
      }

      &--delete {
        color: var(--el-color-danger);
      }

      &--warning {
        color: var(--el-color-warning);
      }
    }

    &__empty {
      padding: 40px 0;
      text-align: center;
    }

    &__footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 16px;
      border-top: 1px solid var(--el-border-color-light);

      &-info {
        flex: 1;
      }

      &-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
</style>
