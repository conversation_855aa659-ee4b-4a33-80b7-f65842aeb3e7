<template>
  <firefly-dialog
    v-model="dialogVisible"
    title="创建分享"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="fms-create-share-dialog">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="分享内容" prop="targetId">
          <div class="fms-create-share-dialog__target-selector">
            <el-radio-group
              v-model="formData.targetType"
              @change="handleTargetTypeChange"
            >
              <el-radio label="directory">
                <el-icon><Folder /></el-icon>
                目录
              </el-radio>
              <el-radio label="file">
                <el-icon><Document /></el-icon>
                文件
              </el-radio>
            </el-radio-group>

            <target-selector
              v-model="formData.targetId"
              :target-type="formData.targetType"
              :show-path="true"
              :show-tree="formData.targetType === 'directory'"
              @change="handleTargetChange"
            />
          </div>
        </el-form-item>

        <el-form-item label="访问控制" prop="accessType">
          <el-radio-group
            v-model="formData.accessType"
            @change="handleAccessTypeChange"
          >
            <el-radio label="public">
              <div class="fms-create-share-dialog__access-option">
                <el-icon><Unlock /></el-icon>
                <div>
                  <div class="fms-create-share-dialog__access-title">
                    公开访问
                  </div>
                  <div class="fms-create-share-dialog__access-desc">
                    任何人都可以通过链接访问
                  </div>
                </div>
              </div>
            </el-radio>
            <el-radio label="password">
              <div class="fms-create-share-dialog__access-option">
                <el-icon><Lock /></el-icon>
                <div>
                  <div class="fms-create-share-dialog__access-title">
                    密码保护
                  </div>
                  <div class="fms-create-share-dialog__access-desc">
                    需要输入密码才能访问
                  </div>
                </div>
              </div>
            </el-radio>
            <el-radio label="view_only">
              <div class="fms-create-share-dialog__access-option">
                <el-icon><View /></el-icon>
                <div>
                  <div class="fms-create-share-dialog__access-title">
                    仅查看
                  </div>
                  <div class="fms-create-share-dialog__access-desc">
                    只能查看，不能下载
                  </div>
                </div>
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="formData.accessType === 'password'"
          label="访问密码"
          prop="password"
        >
          <el-input
            v-model="formData.password"
            type="password"
            placeholder="请输入访问密码"
            show-password
            maxlength="20"
          >
            <template #append>
              <el-button @click="generatePassword">随机生成</el-button>
            </template>
          </el-input>
          <div class="fms-create-share-dialog__password-tip">
            密码长度4-20位，建议包含字母和数字
          </div>
        </el-form-item>

        <el-form-item label="有效期" prop="expiryType">
          <el-radio-group
            v-model="formData.expiryType"
            @change="handleExpiryTypeChange"
          >
            <el-radio label="permanent">永久有效</el-radio>
            <el-radio label="7days">7天</el-radio>
            <el-radio label="30days">30天</el-radio>
            <el-radio label="custom">自定义</el-radio>
          </el-radio-group>

          <el-date-picker
            v-if="formData.expiryType === 'custom'"
            v-model="formData.expiresAt"
            type="datetime"
            placeholder="选择过期时间"
            :disabled-date="disabledDate"
            style="margin-top: 8px; width: 100%"
          />
        </el-form-item>

        <el-form-item label="访问限制">
          <div class="fms-create-share-dialog__limits">
            <el-checkbox v-model="formData.enableViewLimit">
              限制访问次数
            </el-checkbox>
            <el-input-number
              v-if="formData.enableViewLimit"
              v-model="formData.maxViews"
              :min="1"
              :max="10000"
              placeholder="最大访问次数"
              style="margin-left: 12px; width: 150px"
            />
          </div>

          <div class="fms-create-share-dialog__limits" style="margin-top: 8px">
            <el-checkbox v-model="formData.enableDownloadLimit">
              限制下载次数
            </el-checkbox>
            <el-input-number
              v-if="formData.enableDownloadLimit"
              v-model="formData.maxDownloads"
              :min="1"
              :max="1000"
              placeholder="最大下载次数"
              style="margin-left: 12px; width: 150px"
            />
          </div>
        </el-form-item>

        <el-form-item label="分享描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分享描述（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="通知设置">
          <el-checkbox-group v-model="formData.notifications">
            <el-checkbox label="access">有人访问时通知我</el-checkbox>
            <el-checkbox label="download">有人下载时通知我</el-checkbox>
            <el-checkbox label="expire">分享即将过期时通知我</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <!-- 分享预览 -->
      <div v-if="previewData" class="fms-create-share-dialog__preview">
        <div class="fms-create-share-dialog__preview-header">
          <el-icon><View /></el-icon>
          <span>分享预览</span>
        </div>
        <div class="fms-create-share-dialog__preview-content">
          <div class="fms-create-share-dialog__preview-item">
            <span class="label">分享内容：</span>
            <span class="value">{{ previewData.targetName }}</span>
          </div>
          <div class="fms-create-share-dialog__preview-item">
            <span class="label">访问方式：</span>
            <span class="value">
              {{ getAccessTypeLabel(formData.accessType) }}
            </span>
          </div>
          <div class="fms-create-share-dialog__preview-item">
            <span class="label">有效期：</span>
            <span class="value">{{ getExpiryLabel() }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="fms-create-share-dialog__footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="isSubmitting" @click="handleSubmit">
          创建分享
        </el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import {
    ref,
    reactive,
    computed,
    watch,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import { Folder, Document, Lock, Unlock, View } from '@element-plus/icons-vue'
  import { useShareStore } from '../../stores/shareStore'
  import TargetSelector from '../acl/TargetSelector.vue'

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible', 'success'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const shareStore = useShareStore()

  // 响应式数据
  const formRef = ref()
  const isSubmitting = ref(false)
  const previewData = ref(null)

  const formData = reactive({
    targetType: 'directory',
    targetId: null,
    accessType: 'public',
    password: '',
    expiryType: 'permanent',
    expiresAt: null,
    enableViewLimit: false,
    maxViews: 100,
    enableDownloadLimit: false,
    maxDownloads: 10,
    description: '',
    notifications: [],
  })

  // 表单验证规则
  const formRules = {
    targetId: [
      { required: true, message: '请选择分享内容', trigger: 'change' },
    ],
    accessType: [
      { required: true, message: '请选择访问控制方式', trigger: 'change' },
    ],
    password: [
      {
        validator: (rule, value, callback) => {
          if (formData.accessType === 'password') {
            if (!value) {
              callback(new Error('请输入访问密码'))
            } else if (value.length < 4) {
              callback(new Error('密码长度至少4位'))
            } else {
              callback()
            }
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
    expiresAt: [
      {
        validator: (rule, value, callback) => {
          if (formData.expiryType === 'custom' && !value) {
            callback(new Error('请选择过期时间'))
          } else {
            callback()
          }
        },
        trigger: 'change',
      },
    ],
  }

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible) {
        resetForm()
      }
    }
  )

  // 方法
  const resetForm = () => {
    formData.targetType = 'directory'
    formData.targetId = null
    formData.accessType = 'public'
    formData.password = ''
    formData.expiryType = 'permanent'
    formData.expiresAt = null
    formData.enableViewLimit = false
    formData.maxViews = 100
    formData.enableDownloadLimit = false
    formData.maxDownloads = 10
    formData.description = ''
    formData.notifications = []

    previewData.value = null

    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  const handleTargetTypeChange = () => {
    formData.targetId = null
    previewData.value = null
  }

  const handleTargetChange = (target) => {
    previewData.value = target
  }

  const handleAccessTypeChange = () => {
    if (formData.accessType !== 'password') {
      formData.password = ''
    }
  }

  const handleExpiryTypeChange = () => {
    if (formData.expiryType !== 'custom') {
      formData.expiresAt = null
    }
  }

  const generatePassword = () => {
    const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
    let password = ''
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    formData.password = password
  }

  const disabledDate = (time) => {
    return time.getTime() < Date.now()
  }

  const getAccessTypeLabel = (accessType) => {
    const labelMap = {
      public: '公开访问',
      password: '密码保护',
      view_only: '仅查看',
    }
    return labelMap[accessType] || accessType
  }

  const getExpiryLabel = () => {
    switch (formData.expiryType) {
      case 'permanent':
        return '永久有效'
      case '7days':
        return '7天后过期'
      case '30days':
        return '30天后过期'
      case 'custom':
        return formData.expiresAt
          ? new Date(formData.expiresAt).toLocaleString('zh-CN')
          : '自定义时间'
      default:
        return formData.expiryType
    }
  }

  const calculateExpiresAt = () => {
    const now = new Date()
    switch (formData.expiryType) {
      case '7days':
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
      case '30days':
        return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
      case 'custom':
        return formData.expiresAt
      default:
        return null
    }
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      isSubmitting.value = true

      const submitData = {
        targetType: formData.targetType,
        targetId: formData.targetId,
        accessType: formData.accessType,
        password: formData.accessType === 'password' ? formData.password : null,
        expiresAt: calculateExpiresAt(),
        maxViews: formData.enableViewLimit ? formData.maxViews : null,
        maxDownloads: formData.enableDownloadLimit
          ? formData.maxDownloads
          : null,
        description: formData.description.trim() || null,
        notifications: formData.notifications,
      }

      await shareStore.createShare(submitData)

      $baseMessage('分享创建成功', 'success', 'vab-hey-message-success')
      emit('success')
      handleClose()
    } catch (error) {
      $baseMessage(
        error.message || '分享创建失败',
        'error',
        'vab-hey-message-error'
      )
    } finally {
      isSubmitting.value = false
    }
  }

  const handleClose = () => {
    dialogVisible.value = false
  }
</script>

<style lang="scss" scoped>
  .fms-create-share-dialog {
    &__target-selector {
      .el-radio-group {
        margin-bottom: 12px;
      }
    }

    &__access-option {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      width: 100%;

      .el-icon {
        font-size: 16px;
        color: var(--el-color-primary);
        margin-top: 2px;
      }
    }

    &__access-title {
      font-weight: 500;
      color: var(--el-text-color-primary);
      margin-bottom: 2px;
    }

    &__access-desc {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }

    &__password-tip {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
      margin-top: 4px;
    }

    &__limits {
      display: flex;
      align-items: center;
    }

    &__preview {
      margin-top: 24px;
      padding: 16px;
      background: var(--el-bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);

      &-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 12px;

        .el-icon {
          color: var(--el-color-primary);
        }
      }

      &-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      &-item {
        display: flex;
        align-items: center;

        .label {
          font-size: 13px;
          color: var(--el-text-color-regular);
          width: 80px;
          flex-shrink: 0;
        }

        .value {
          font-size: 13px;
          color: var(--el-text-color-primary);
          font-weight: 500;
        }
      }
    }

    &__footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  :deep(.el-radio) {
    display: flex;
    align-items: flex-start;
    margin-right: 0;
    margin-bottom: 12px;
    width: 100%;

    .el-radio__label {
      width: 100%;
      padding-left: 8px;
    }
  }

  :deep(.el-checkbox) {
    margin-right: 24px;
    margin-bottom: 8px;
  }
</style>
