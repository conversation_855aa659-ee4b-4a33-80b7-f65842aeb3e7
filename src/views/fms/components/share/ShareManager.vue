<template>
  <div class="fms-share-manager">
    <div class="fms-share-manager__header">
      <div class="fms-share-manager__title">
        <el-icon><Share /></el-icon>
        <span>分享管理</span>
      </div>
      <div class="fms-share-manager__actions">
        <el-button type="primary" :icon="Plus" @click="handleCreateShare">
          创建分享
        </el-button>
        <el-button
          v-if="selectedShares.length > 0"
          type="danger"
          :icon="Delete"
          @click="handleBatchRevoke"
        >
          批量撤销
        </el-button>
      </div>
    </div>

    <div class="fms-share-manager__filters">
      <filter-panel
        :fields="filterFields"
        :values="filters"
        @change="handleFilterChange"
        @reset="handleFilterReset"
      />
    </div>

    <div class="fms-share-manager__stats">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-statistic title="总分享数" :value="stats.total" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="活跃分享" :value="stats.active" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="今日访问" :value="stats.todayViews" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="总访问量" :value="stats.totalViews" />
        </el-col>
      </el-row>
    </div>

    <div class="fms-share-manager__content">
      <el-table
        ref="tableRef"
        :data="shares"
        v-loading="isLoading"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="分享内容" min-width="250">
          <template #default="{ row }">
            <div class="fms-share-manager__target">
              <el-icon class="fms-share-manager__target-icon">
                <component :is="getTargetIcon(row.targetType)" />
              </el-icon>
              <div class="fms-share-manager__target-info">
                <div class="fms-share-manager__target-name">
                  {{ row.targetName }}
                </div>
                <div class="fms-share-manager__target-path">
                  {{ row.targetPath }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="分享链接" min-width="200">
          <template #default="{ row }">
            <div class="fms-share-manager__link">
              <el-input
                :value="getShareUrl(row.shareCode)"
                readonly
                size="small"
              >
                <template #append>
                  <el-button
                    :icon="CopyDocument"
                    @click="copyShareUrl(row.shareCode)"
                  />
                </template>
              </el-input>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="访问控制" width="120">
          <template #default="{ row }">
            <div class="fms-share-manager__access">
              <el-tag :type="getAccessTypeTag(row.accessType)" size="small">
                {{ getAccessTypeLabel(row.accessType) }}
              </el-tag>
              <div v-if="row.password" class="fms-share-manager__password">
                <el-icon><Lock /></el-icon>
                <span>需要密码</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="访问统计" width="100" align="center">
          <template #default="{ row }">
            <div class="fms-share-manager__views">
              <span class="fms-share-manager__view-count">
                {{ row.viewCount || 0 }}
              </span>
              <span class="fms-share-manager__view-label">次访问</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="有效期" width="120">
          <template #default="{ row }">
            <div class="fms-share-manager__expiry">
              <span v-if="row.expiresAt" class="fms-share-manager__expiry-time">
                {{ formatDateTime(row.expiresAt) }}
              </span>
              <el-tag v-else type="success" size="small">永久有效</el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            <span class="fms-share-manager__time">
              {{ formatDateTime(row.createdAt) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="fms-share-manager__row-actions">
              <el-button type="text" size="small" @click="handleViewShare(row)">
                查看
              </el-button>
              <el-button type="text" size="small" @click="handleEditShare(row)">
                编辑
              </el-button>
              <el-button type="text" size="small" @click="handleViewStats(row)">
                统计
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleRevokeShare(row)"
                class="danger"
              >
                撤销
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="fms-share-manager__pagination">
        <pagination
          :current="pagination.current"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          @change="handlePageChange"
        />
      </div>
    </div>

    <!-- 创建分享对话框 -->
    <create-share-dialog
      v-model:visible="showCreateDialog"
      @success="handleCreateSuccess"
    />

    <!-- 分享链接查看器 -->
    <share-link-viewer v-model:visible="showViewDialog" :share="viewingShare" />

    <!-- 分享统计对话框 -->
    <share-stats-dialog v-model:visible="showStatsDialog" :share="statsShare" />
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, inject } from 'vue'
  import {
    Share,
    Plus,
    Delete,
    CopyDocument,
    Lock,
    Folder,
    Document,
  } from '@element-plus/icons-vue'
  import { useShareStore } from '../../stores/shareStore'
  import FilterPanel from '../common/FilterPanel.vue'
  import Pagination from '../common/Pagination.vue'
  import CreateShareDialog from './CreateShareDialog.vue'
  import ShareLinkViewer from './ShareLinkViewer.vue'
  import ShareStatsDialog from './ShareStatsDialog.vue'

  // 注入服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // Store
  const shareStore = useShareStore()

  // 响应式数据
  const tableRef = ref()
  const selectedShares = ref([])
  const showCreateDialog = ref(false)
  const showViewDialog = ref(false)
  const showStatsDialog = ref(false)
  const viewingShare = ref(null)
  const statsShare = ref(null)
  const stats = ref({
    total: 0,
    active: 0,
    todayViews: 0,
    totalViews: 0,
  })

  // 筛选字段配置
  const filterFields = [
    {
      key: 'keyword',
      label: '关键词',
      type: 'input',
      placeholder: '搜索分享内容或链接',
    },
    {
      key: 'targetType',
      label: '类型',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '目录', value: 'directory' },
        { label: '文件', value: 'file' },
      ],
    },
    {
      key: 'accessType',
      label: '访问控制',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '公开', value: 'public' },
        { label: '需要密码', value: 'password' },
        { label: '仅查看', value: 'view_only' },
      ],
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '活跃', value: 'active' },
        { label: '已过期', value: 'expired' },
        { label: '已撤销', value: 'revoked' },
      ],
    },
  ]

  // 计算属性
  const isLoading = computed(() => shareStore.isLoading)
  const shares = computed(() => shareStore.shares)
  const pagination = computed(() => shareStore.pagination)
  const filters = computed(() => shareStore.filters)

  // 方法
  const loadShares = async () => {
    try {
      await shareStore.fetchShares()
    } catch (error) {
      $baseMessage('加载分享列表失败', 'error', 'vab-hey-message-error')
    }
  }

  const loadStats = async () => {
    try {
      const data = await shareStore.fetchShareStats()
      stats.value = data
    } catch (error) {
      console.warn('加载分享统计失败:', error)
    }
  }

  const handleCreateShare = () => {
    showCreateDialog.value = true
  }

  const handleCreateSuccess = () => {
    loadShares()
    loadStats()
  }

  const handleViewShare = (share) => {
    viewingShare.value = share
    showViewDialog.value = true
  }

  const handleEditShare = (share) => {
    // TODO: 实现编辑分享
    $baseMessage('编辑分享功能开发中', 'info', 'vab-hey-message-info')
  }

  const handleViewStats = (share) => {
    statsShare.value = share
    showStatsDialog.value = true
  }

  const handleRevokeShare = (share) => {
    $baseConfirm(
      `确认撤销分享 "${share.targetName}"？撤销后分享链接将失效。`,
      () => {},
      async () => {
        try {
          await shareStore.revokeShare(share.id)
          $baseMessage('分享撤销成功', 'success', 'vab-hey-message-success')
          loadStats()
        } catch (error) {
          $baseMessage('分享撤销失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handleBatchRevoke = () => {
    const shareNames = selectedShares.value
      .map((share) => share.targetName)
      .join('、')

    $baseConfirm(
      `确认撤销选中的 ${selectedShares.value.length} 个分享（${shareNames}）？`,
      () => {},
      async () => {
        try {
          const ids = selectedShares.value.map((share) => share.id)
          await shareStore.batchRevokeShares(ids)

          $baseMessage('批量撤销成功', 'success', 'vab-hey-message-success')
          selectedShares.value = []
          loadStats()
        } catch (error) {
          $baseMessage('批量撤销失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handleSelectionChange = (selection) => {
    selectedShares.value = selection
  }

  const handleFilterChange = (newFilters) => {
    shareStore.updateFilters(newFilters)
  }

  const handleFilterReset = () => {
    shareStore.resetFilters()
  }

  const handlePageChange = (page, pageSize) => {
    shareStore.fetchShares({ page, pageSize })
  }

  const copyShareUrl = async (shareCode) => {
    const url = getShareUrl(shareCode)

    try {
      await navigator.clipboard.writeText(url)
      $baseMessage(
        '分享链接已复制到剪贴板',
        'success',
        'vab-hey-message-success'
      )
    } catch (error) {
      $baseMessage('复制失败', 'error', 'vab-hey-message-error')
    }
  }

  // 工具方法
  const getShareUrl = (shareCode) => {
    return `${window.location.origin}/share/${shareCode}`
  }

  const getTargetIcon = (targetType) => {
    const iconMap = {
      directory: Folder,
      file: Document,
    }
    return iconMap[targetType] || Document
  }

  const getAccessTypeTag = (accessType) => {
    const tagMap = {
      public: 'success',
      password: 'warning',
      view_only: 'info',
    }
    return tagMap[accessType] || 'info'
  }

  const getAccessTypeLabel = (accessType) => {
    const labelMap = {
      public: '公开',
      password: '需要密码',
      view_only: '仅查看',
    }
    return labelMap[accessType] || accessType
  }

  const getStatusTag = (status) => {
    const tagMap = {
      active: 'success',
      expired: 'warning',
      revoked: 'danger',
    }
    return tagMap[status] || 'info'
  }

  const getStatusLabel = (status) => {
    const labelMap = {
      active: '活跃',
      expired: '已过期',
      revoked: '已撤销',
    }
    return labelMap[status] || status
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 生命周期
  onMounted(() => {
    loadShares()
    loadStats()
  })
</script>

<style lang="scss" scoped>
  .fms-share-manager {
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }

    &__title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    &__actions {
      display: flex;
      gap: 8px;
    }

    &__filters {
      margin-bottom: 16px;
    }

    &__stats {
      margin-bottom: 24px;
      padding: 16px;
      background: var(--el-bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__content {
      background: white;
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__target {
      display: flex;
      align-items: center;
      gap: 8px;

      &-icon {
        font-size: 16px;
        color: var(--el-color-primary);
        flex-shrink: 0;
      }

      &-info {
        flex: 1;
        min-width: 0;
      }

      &-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-path {
        font-size: 12px;
        color: var(--el-text-color-regular);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    &__link {
      :deep(.el-input-group__append) {
        padding: 0;

        .el-button {
          border: none;
          padding: 8px;
        }
      }
    }

    &__access {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    &__password {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: var(--el-text-color-regular);

      .el-icon {
        font-size: 12px;
      }
    }

    &__views {
      text-align: center;
    }

    &__view-count {
      display: block;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-color-primary);
    }

    &__view-label {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }

    &__expiry {
      &-time {
        font-size: 13px;
        color: var(--el-text-color-regular);
      }
    }

    &__time {
      font-size: 13px;
      color: var(--el-text-color-regular);
    }

    &__row-actions {
      display: flex;
      gap: 4px;

      .danger {
        color: var(--el-color-danger);

        &:hover {
          color: var(--el-color-danger);
        }
      }
    }

    &__pagination {
      padding: 16px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }

  :deep(.el-statistic) {
    text-align: center;

    .el-statistic__head {
      font-size: 13px;
      color: var(--el-text-color-regular);
      margin-bottom: 4px;
    }

    .el-statistic__content {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-color-primary);
    }
  }
</style>
