<template>
  <firefly-dialog
    v-model="dialogVisible"
    title="分享链接"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="fms-share-link-viewer">
      <div v-if="share" class="fms-share-link-viewer__content">
        <!-- 分享信息 -->
        <div class="fms-share-link-viewer__info">
          <div class="fms-share-link-viewer__target">
            <el-icon class="fms-share-link-viewer__target-icon">
              <component :is="getTargetIcon(share.targetType)" />
            </el-icon>
            <div class="fms-share-link-viewer__target-info">
              <div class="fms-share-link-viewer__target-name">
                {{ share.targetName }}
              </div>
              <div class="fms-share-link-viewer__target-path">
                {{ share.targetPath }}
              </div>
            </div>
          </div>

          <div class="fms-share-link-viewer__meta">
            <div class="fms-share-link-viewer__meta-item">
              <span class="label">访问方式：</span>
              <el-tag :type="getAccessTypeTag(share.accessType)" size="small">
                {{ getAccessTypeLabel(share.accessType) }}
              </el-tag>
            </div>
            <div class="fms-share-link-viewer__meta-item">
              <span class="label">有效期：</span>
              <span class="value">
                {{
                  share.expiresAt ? formatDateTime(share.expiresAt) : '永久有效'
                }}
              </span>
            </div>
            <div class="fms-share-link-viewer__meta-item">
              <span class="label">访问次数：</span>
              <span class="value">{{ share.viewCount || 0 }} 次</span>
            </div>
            <div class="fms-share-link-viewer__meta-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatDateTime(share.createdAt) }}</span>
            </div>
          </div>
        </div>

        <!-- 分享链接 -->
        <div class="fms-share-link-viewer__link-section">
          <div class="fms-share-link-viewer__section-title">
            <el-icon><Link /></el-icon>
            <span>分享链接</span>
          </div>

          <div class="fms-share-link-viewer__link-content">
            <el-input :value="shareUrl" readonly size="large">
              <template #append>
                <el-button :icon="CopyDocument" @click="copyShareUrl">
                  复制
                </el-button>
              </template>
            </el-input>

            <div class="fms-share-link-viewer__link-actions">
              <el-button
                type="primary"
                :icon="ExternalLink"
                @click="openShareLink"
              >
                打开链接
              </el-button>
              <el-button :icon="QrCode" @click="showQrCode = true">
                二维码
              </el-button>
            </div>
          </div>
        </div>

        <!-- 访问密码 -->
        <div
          v-if="share.password"
          class="fms-share-link-viewer__password-section"
        >
          <div class="fms-share-link-viewer__section-title">
            <el-icon><Lock /></el-icon>
            <span>访问密码</span>
          </div>

          <div class="fms-share-link-viewer__password-content">
            <el-input :value="share.password" readonly size="large">
              <template #append>
                <el-button :icon="CopyDocument" @click="copyPassword">
                  复制
                </el-button>
              </template>
            </el-input>

            <div class="fms-share-link-viewer__password-tip">
              访问者需要输入此密码才能查看分享内容
            </div>
          </div>
        </div>

        <!-- 分享描述 -->
        <div
          v-if="share.description"
          class="fms-share-link-viewer__description-section"
        >
          <div class="fms-share-link-viewer__section-title">
            <el-icon><Document /></el-icon>
            <span>分享描述</span>
          </div>

          <div class="fms-share-link-viewer__description-content">
            {{ share.description }}
          </div>
        </div>

        <!-- 访问限制 -->
        <div v-if="hasLimits" class="fms-share-link-viewer__limits-section">
          <div class="fms-share-link-viewer__section-title">
            <el-icon><Warning /></el-icon>
            <span>访问限制</span>
          </div>

          <div class="fms-share-link-viewer__limits-content">
            <div
              v-if="share.maxViews"
              class="fms-share-link-viewer__limit-item"
            >
              <span class="label">访问次数限制：</span>
              <span class="value">
                {{ share.viewCount || 0 }} / {{ share.maxViews }}
              </span>
              <el-progress
                :percentage="getViewProgress()"
                :color="getProgressColor(getViewProgress())"
                :show-text="false"
                style="margin-left: 12px; flex: 1"
              />
            </div>

            <div
              v-if="share.maxDownloads"
              class="fms-share-link-viewer__limit-item"
            >
              <span class="label">下载次数限制：</span>
              <span class="value">
                {{ share.downloadCount || 0 }} / {{ share.maxDownloads }}
              </span>
              <el-progress
                :percentage="getDownloadProgress()"
                :color="getProgressColor(getDownloadProgress())"
                :show-text="false"
                style="margin-left: 12px; flex: 1"
              />
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="fms-share-link-viewer__quick-actions">
          <el-button type="primary" :icon="Share" @click="handleQuickShare">
            快速分享
          </el-button>
          <el-button :icon="Edit" @click="handleEdit">编辑分享</el-button>
          <el-button :icon="DataAnalysis" @click="handleViewStats">
            查看统计
          </el-button>
        </div>
      </div>

      <!-- 二维码对话框 -->
      <el-dialog
        v-model="showQrCode"
        title="分享二维码"
        width="400px"
        align-center
      >
        <div class="fms-share-link-viewer__qrcode">
          <div
            ref="qrcodeRef"
            class="fms-share-link-viewer__qrcode-container"
          ></div>
          <div class="fms-share-link-viewer__qrcode-tip">
            扫描二维码访问分享内容
          </div>
        </div>
      </el-dialog>
    </div>

    <template #footer>
      <div class="fms-share-link-viewer__footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import {
    ref,
    computed,
    watch,
    nextTick,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import {
    Link,
    CopyDocument,
    ExternalLink,
    QrCode,
    Lock,
    Document,
    Warning,
    Share,
    Edit,
    DataAnalysis,
    Folder,
    Document as DocumentIcon,
  } from '@element-plus/icons-vue'

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    share: {
      type: Object,
      default: null,
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible', 'edit', 'view-stats'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // 响应式数据
  const qrcodeRef = ref()
  const showQrCode = ref(false)

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  const shareUrl = computed(() => {
    if (!props.share) return ''
    return `${window.location.origin}/share/${props.share.shareCode}`
  })

  const hasLimits = computed(() => {
    return props.share && (props.share.maxViews || props.share.maxDownloads)
  })

  // 监听二维码对话框显示
  watch(showQrCode, async (visible) => {
    if (visible) {
      await nextTick()
      generateQrCode()
    }
  })

  // 方法
  const copyShareUrl = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl.value)
      $baseMessage(
        '分享链接已复制到剪贴板',
        'success',
        'vab-hey-message-success'
      )
    } catch (error) {
      $baseMessage('复制失败', 'error', 'vab-hey-message-error')
    }
  }

  const copyPassword = async () => {
    try {
      await navigator.clipboard.writeText(props.share.password)
      $baseMessage(
        '访问密码已复制到剪贴板',
        'success',
        'vab-hey-message-success'
      )
    } catch (error) {
      $baseMessage('复制失败', 'error', 'vab-hey-message-error')
    }
  }

  const openShareLink = () => {
    window.open(shareUrl.value, '_blank')
  }

  const generateQrCode = () => {
    // 这里应该使用二维码生成库，如 qrcode.js
    // 为了演示，我们创建一个占位符
    if (qrcodeRef.value) {
      qrcodeRef.value.innerHTML = `
      <div style="width: 200px; height: 200px; border: 1px solid #ddd; display: flex; align-items: center; justify-content: center; background: #f5f5f5; margin: 0 auto;">
        <span style="color: #999; font-size: 14px;">二维码占位符</span>
      </div>
    `
    }
  }

  const handleQuickShare = async () => {
    const shareText = `${props.share.targetName}\n${shareUrl.value}`

    if (navigator.share) {
      try {
        await navigator.share({
          title: props.share.targetName,
          text: shareText,
          url: shareUrl.value,
        })
      } catch (error) {
        // 用户取消分享或不支持
        copyShareUrl()
      }
    } else {
      copyShareUrl()
    }
  }

  const handleEdit = () => {
    emit('edit', props.share)
  }

  const handleViewStats = () => {
    emit('view-stats', props.share)
  }

  const handleClose = () => {
    dialogVisible.value = false
  }

  // 工具方法
  const getTargetIcon = (targetType) => {
    const iconMap = {
      directory: Folder,
      file: DocumentIcon,
    }
    return iconMap[targetType] || DocumentIcon
  }

  const getAccessTypeTag = (accessType) => {
    const tagMap = {
      public: 'success',
      password: 'warning',
      view_only: 'info',
    }
    return tagMap[accessType] || 'info'
  }

  const getAccessTypeLabel = (accessType) => {
    const labelMap = {
      public: '公开访问',
      password: '密码保护',
      view_only: '仅查看',
    }
    return labelMap[accessType] || accessType
  }

  const getViewProgress = () => {
    if (!props.share || !props.share.maxViews) return 0
    return Math.round(
      ((props.share.viewCount || 0) / props.share.maxViews) * 100
    )
  }

  const getDownloadProgress = () => {
    if (!props.share || !props.share.maxDownloads) return 0
    return Math.round(
      ((props.share.downloadCount || 0) / props.share.maxDownloads) * 100
    )
  }

  const getProgressColor = (percentage) => {
    if (percentage >= 90) return '#f56c6c'
    if (percentage >= 70) return '#e6a23c'
    return '#67c23a'
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }
</script>

<style lang="scss" scoped>
  .fms-share-link-viewer {
    &__content {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    &__info {
      padding: 16px;
      background: var(--el-bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__target {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      &-icon {
        font-size: 24px;
        color: var(--el-color-primary);
      }

      &-info {
        flex: 1;
      }

      &-name {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }

      &-path {
        font-size: 13px;
        color: var(--el-text-color-regular);
        font-family: 'Monaco', 'Menlo', monospace;
      }
    }

    &__meta {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      &-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .label {
          font-size: 13px;
          color: var(--el-text-color-regular);
          flex-shrink: 0;
        }

        .value {
          font-size: 13px;
          color: var(--el-text-color-primary);
          font-weight: 500;
        }
      }
    }

    &__section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 12px;

      .el-icon {
        color: var(--el-color-primary);
      }
    }

    &__link-section,
    &__password-section,
    &__description-section,
    &__limits-section {
      padding: 16px;
      background: white;
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__link-content {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    &__link-actions {
      display: flex;
      gap: 8px;
    }

    &__password-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    &__password-tip {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
    }

    &__description-content {
      font-size: 14px;
      color: var(--el-text-color-primary);
      line-height: 1.6;
      white-space: pre-wrap;
    }

    &__limits-content {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    &__limit-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .label {
        font-size: 13px;
        color: var(--el-text-color-regular);
        flex-shrink: 0;
        width: 120px;
      }

      .value {
        font-size: 13px;
        color: var(--el-text-color-primary);
        font-weight: 500;
        flex-shrink: 0;
        width: 60px;
      }
    }

    &__quick-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
      padding: 16px;
      background: var(--el-bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__qrcode {
      text-align: center;

      &-container {
        margin-bottom: 16px;
      }

      &-tip {
        font-size: 13px;
        color: var(--el-text-color-regular);
      }
    }

    &__footer {
      display: flex;
      justify-content: flex-end;
    }
  }

  :deep(.el-input-group__append) {
    padding: 0;

    .el-button {
      border: none;
      padding: 8px 16px;
    }
  }

  :deep(.el-progress-bar__outer) {
    height: 6px;
  }
</style>
