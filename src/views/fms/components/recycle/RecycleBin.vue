<template>
  <div class="fms-recycle-bin">
    <div class="fms-recycle-bin__header">
      <div class="fms-recycle-bin__title">
        <el-icon><Delete /></el-icon>
        <span>回收站</span>
      </div>
      <div class="fms-recycle-bin__actions">
        <el-button
          v-if="selectedItems.length > 0"
          type="primary"
          :icon="RefreshRight"
          @click="handleBatchRestore"
        >
          批量恢复
        </el-button>
        <el-button
          v-if="selectedItems.length > 0"
          type="danger"
          :icon="Delete"
          @click="handleBatchDelete"
        >
          彻底删除
        </el-button>
        <el-button type="warning" :icon="Delete" @click="handleEmptyRecycleBin">
          清空回收站
        </el-button>
      </div>
    </div>

    <div class="fms-recycle-bin__filters">
      <filter-panel
        :fields="filterFields"
        :values="filters"
        @change="handleFilterChange"
        @reset="handleFilterReset"
      />
    </div>

    <div class="fms-recycle-bin__stats">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-statistic title="回收站项目" :value="stats.total" />
        </el-col>
        <el-col :span="8">
          <el-statistic
            title="占用空间"
            :value="formatFileSize(stats.totalSize)"
          />
        </el-col>
        <el-col :span="8">
          <el-statistic title="即将过期" :value="stats.expiringSoon" />
        </el-col>
      </el-row>
    </div>

    <div class="fms-recycle-bin__content">
      <el-table
        ref="tableRef"
        :data="recycleItems"
        v-loading="isLoading"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="名称" min-width="250">
          <template #default="{ row }">
            <div class="fms-recycle-bin__item">
              <el-icon class="fms-recycle-bin__item-icon">
                <component :is="getItemIcon(row.type)" />
              </el-icon>
              <div class="fms-recycle-bin__item-info">
                <div class="fms-recycle-bin__item-name">
                  {{ row.name }}
                </div>
                <div class="fms-recycle-bin__item-path">
                  原路径：{{ row.originalPath }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="类型" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getTypeTag(row.type)" size="small">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="大小" width="100" align="right">
          <template #default="{ row }">
            <span v-if="row.size !== undefined" class="fms-recycle-bin__size">
              {{ formatFileSize(row.size) }}
            </span>
            <span v-else class="fms-recycle-bin__size-placeholder">-</span>
          </template>
        </el-table-column>

        <el-table-column label="删除者" width="120">
          <template #default="{ row }">
            <div class="fms-recycle-bin__user">
              <el-avatar
                v-if="row.deletedBy?.avatar"
                :src="row.deletedBy.avatar"
                :size="24"
              />
              <el-icon v-else class="fms-recycle-bin__user-icon">
                <User />
              </el-icon>
              <span class="fms-recycle-bin__user-name">
                {{ row.deletedBy?.name || '未知' }}
              </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="删除时间" width="160">
          <template #default="{ row }">
            <span class="fms-recycle-bin__time">
              {{ formatDateTime(row.deletedAt) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="过期时间" width="160">
          <template #default="{ row }">
            <div class="fms-recycle-bin__expiry">
              <span
                :class="[
                  'fms-recycle-bin__expiry-time',
                  { 'is-warning': isExpiringSoon(row.expiresAt) },
                ]"
              >
                {{ formatDateTime(row.expiresAt) }}
              </span>
              <el-tag
                v-if="isExpiringSoon(row.expiresAt)"
                type="warning"
                size="small"
                style="margin-left: 8px"
              >
                即将过期
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="fms-recycle-bin__row-actions">
              <el-button
                type="text"
                size="small"
                @click="handlePreview(row)"
                :disabled="!canPreview(row)"
              >
                预览
              </el-button>
              <el-button type="text" size="small" @click="handleRestore(row)">
                恢复
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handlePermanentDelete(row)"
                class="danger"
              >
                彻底删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="fms-recycle-bin__pagination">
        <pagination
          :current="pagination.current"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          @change="handlePageChange"
        />
      </div>
    </div>

    <!-- 恢复确认对话框 -->
    <restore-confirm-dialog
      v-model:visible="showRestoreDialog"
      :items="restoreItems"
      @success="handleRestoreSuccess"
    />

    <!-- 文件预览对话框 -->
    <file-preview
      v-if="previewFile"
      v-model:visible="showPreviewDialog"
      :file="previewFile"
      :readonly="true"
    />
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, inject } from 'vue'
  import {
    Delete,
    RefreshRight,
    User,
    Folder,
    Document,
  } from '@element-plus/icons-vue'
  import { useRecycleStore } from '../../stores/recycleStore'
  import { useFmsStore } from '../../stores/fmsStore'
  import FilterPanel from '../common/FilterPanel.vue'
  import Pagination from '../common/Pagination.vue'
  import RestoreConfirmDialog from './RestoreConfirmDialog.vue'
  import FilePreview from '../file/FilePreview.vue'

  // 注入服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // Store
  const recycleStore = useRecycleStore()
  const fmsStore = useFmsStore()

  // 响应式数据
  const tableRef = ref()
  const selectedItems = ref([])
  const showRestoreDialog = ref(false)
  const showPreviewDialog = ref(false)
  const restoreItems = ref([])
  const previewFile = ref(null)
  const stats = ref({
    total: 0,
    totalSize: 0,
    expiringSoon: 0,
  })

  // 筛选字段配置
  const filterFields = [
    {
      key: 'keyword',
      label: '关键词',
      type: 'input',
      placeholder: '搜索文件或目录名称',
    },
    {
      key: 'type',
      label: '类型',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '目录', value: 'directory' },
        { label: '文件', value: 'file' },
      ],
    },
    {
      key: 'deletedBy',
      label: '删除者',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '我删除的', value: 'me' },
        { label: '他人删除的', value: 'others' },
      ],
    },
    {
      key: 'expiry',
      label: '过期状态',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '即将过期', value: 'expiring' },
        { label: '正常', value: 'normal' },
      ],
    },
  ]

  // 计算属性
  const isLoading = computed(() => recycleStore.isLoading)
  const recycleItems = computed(() => recycleStore.recycleItems)
  const pagination = computed(() => recycleStore.pagination)
  const filters = computed(() => recycleStore.filters)

  // 方法
  const loadRecycleItems = async () => {
    try {
      await recycleStore.fetchRecycleItems()
    } catch (error) {
      $baseMessage('加载回收站失败', 'error', 'vab-hey-message-error')
    }
  }

  const loadStats = async () => {
    try {
      const data = await recycleStore.fetchRecycleStats()
      stats.value = data
    } catch (error) {
      console.warn('加载回收站统计失败:', error)
    }
  }

  const handleRestore = (item) => {
    restoreItems.value = [item]
    showRestoreDialog.value = true
  }

  const handleBatchRestore = () => {
    restoreItems.value = [...selectedItems.value]
    showRestoreDialog.value = true
  }

  const handleRestoreSuccess = () => {
    selectedItems.value = []
    loadRecycleItems()
    loadStats()
  }

  const handlePermanentDelete = (item) => {
    $baseConfirm(
      `确认彻底删除 "${item.name}"？此操作不可恢复！`,
      () => {},
      async () => {
        try {
          await recycleStore.permanentDelete(item.id)
          $baseMessage('彻底删除成功', 'success', 'vab-hey-message-success')
          loadStats()
        } catch (error) {
          $baseMessage('彻底删除失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handleBatchDelete = () => {
    const itemNames = selectedItems.value.map((item) => item.name).join('、')

    $baseConfirm(
      `确认彻底删除选中的 ${selectedItems.value.length} 个项目（${itemNames}）？此操作不可恢复！`,
      () => {},
      async () => {
        try {
          const ids = selectedItems.value.map((item) => item.id)
          await recycleStore.batchPermanentDelete(ids)

          $baseMessage('批量删除成功', 'success', 'vab-hey-message-success')
          selectedItems.value = []
          loadStats()
        } catch (error) {
          $baseMessage('批量删除失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handleEmptyRecycleBin = () => {
    $baseConfirm(
      '确认清空回收站？这将彻底删除所有项目，此操作不可恢复！',
      () => {},
      async () => {
        try {
          await recycleStore.emptyRecycleBin()
          $baseMessage('回收站已清空', 'success', 'vab-hey-message-success')
          selectedItems.value = []
          loadStats()
        } catch (error) {
          $baseMessage('清空回收站失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handlePreview = (item) => {
    if (item.type === 'file') {
      previewFile.value = {
        ...item,
        id: item.originalId || item.id,
        name: item.name,
        path: item.originalPath,
      }
      showPreviewDialog.value = true
    }
  }

  const handleSelectionChange = (selection) => {
    selectedItems.value = selection
  }

  const handleFilterChange = (newFilters) => {
    recycleStore.updateFilters(newFilters)
  }

  const handleFilterReset = () => {
    recycleStore.resetFilters()
  }

  const handlePageChange = (page, pageSize) => {
    recycleStore.fetchRecycleItems({ page, pageSize })
  }

  // 工具方法
  const getItemIcon = (type) => {
    const iconMap = {
      directory: Folder,
      file: Document,
    }
    return iconMap[type] || Document
  }

  const getTypeTag = (type) => {
    const tagMap = {
      directory: 'primary',
      file: 'success',
    }
    return tagMap[type] || 'info'
  }

  const getTypeLabel = (type) => {
    const labelMap = {
      directory: '目录',
      file: '文件',
    }
    return labelMap[type] || type
  }

  const canPreview = (item) => {
    return item.type === 'file' && item.size < 50 * 1024 * 1024 // 50MB以下可预览
  }

  const isExpiringSoon = (expiresAt) => {
    if (!expiresAt) return false
    const now = new Date()
    const expiry = new Date(expiresAt)
    const diffDays = (expiry - now) / (1000 * 60 * 60 * 24)
    return diffDays <= 7 && diffDays > 0
  }

  const formatFileSize = (bytes) => {
    return fmsStore.formatFileSize(bytes)
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 生命周期
  onMounted(() => {
    loadRecycleItems()
    loadStats()
  })
</script>

<style lang="scss" scoped>
  .fms-recycle-bin {
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }

    &__title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    &__actions {
      display: flex;
      gap: 8px;
    }

    &__filters {
      margin-bottom: 16px;
    }

    &__stats {
      margin-bottom: 24px;
      padding: 16px;
      background: var(--el-bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__content {
      background: white;
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__item {
      display: flex;
      align-items: center;
      gap: 8px;

      &-icon {
        font-size: 16px;
        color: var(--el-color-primary);
        flex-shrink: 0;
      }

      &-info {
        flex: 1;
        min-width: 0;
      }

      &-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-path {
        font-size: 12px;
        color: var(--el-text-color-regular);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    &__size {
      font-size: 13px;
      color: var(--el-text-color-regular);

      &-placeholder {
        color: var(--el-text-color-placeholder);
      }
    }

    &__user {
      display: flex;
      align-items: center;
      gap: 6px;

      &-icon {
        font-size: 20px;
        color: var(--el-color-primary);
      }

      &-name {
        font-size: 13px;
        color: var(--el-text-color-primary);
      }
    }

    &__time {
      font-size: 13px;
      color: var(--el-text-color-regular);
    }

    &__expiry {
      display: flex;
      align-items: center;

      &-time {
        font-size: 13px;
        color: var(--el-text-color-regular);

        &.is-warning {
          color: var(--el-color-warning);
          font-weight: 500;
        }
      }
    }

    &__row-actions {
      display: flex;
      gap: 4px;

      .danger {
        color: var(--el-color-danger);

        &:hover {
          color: var(--el-color-danger);
        }
      }
    }

    &__pagination {
      padding: 16px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }

  :deep(.el-statistic) {
    text-align: center;

    .el-statistic__head {
      font-size: 13px;
      color: var(--el-text-color-regular);
      margin-bottom: 4px;
    }

    .el-statistic__content {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-color-primary);
    }
  }
</style>
