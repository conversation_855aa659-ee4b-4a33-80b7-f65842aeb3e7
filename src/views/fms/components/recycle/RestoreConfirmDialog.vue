<template>
  <firefly-dialog
    v-model="dialogVisible"
    title="恢复确认"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="fms-restore-confirm-dialog">
      <div class="fms-restore-confirm-dialog__info">
        <el-alert type="info" :closable="false" show-icon>
          <template #title>
            <div>即将恢复 {{ items.length }} 个项目到原始位置</div>
          </template>
        </el-alert>
      </div>

      <div class="fms-restore-confirm-dialog__items">
        <div class="fms-restore-confirm-dialog__items-header">
          <span>恢复项目列表：</span>
        </div>

        <div class="fms-restore-confirm-dialog__items-list">
          <div
            v-for="item in items"
            :key="item.id"
            class="fms-restore-confirm-dialog__item"
          >
            <el-icon class="fms-restore-confirm-dialog__item-icon">
              <component :is="getItemIcon(item.type)" />
            </el-icon>
            <div class="fms-restore-confirm-dialog__item-info">
              <div class="fms-restore-confirm-dialog__item-name">
                {{ item.name }}
              </div>
              <div class="fms-restore-confirm-dialog__item-path">
                恢复到：{{ item.originalPath }}
              </div>
            </div>
            <div class="fms-restore-confirm-dialog__item-status">
              <el-tag :type="getStatusTag(item.restoreStatus)" size="small">
                {{ getStatusLabel(item.restoreStatus) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 冲突处理 -->
      <div v-if="hasConflicts" class="fms-restore-confirm-dialog__conflicts">
        <div class="fms-restore-confirm-dialog__conflicts-header">
          <el-icon><Warning /></el-icon>
          <span>发现冲突项目</span>
        </div>

        <div class="fms-restore-confirm-dialog__conflicts-list">
          <div
            v-for="conflict in conflictItems"
            :key="conflict.id"
            class="fms-restore-confirm-dialog__conflict"
          >
            <div class="fms-restore-confirm-dialog__conflict-info">
              <el-icon class="fms-restore-confirm-dialog__conflict-icon">
                <component :is="getItemIcon(conflict.type)" />
              </el-icon>
              <div class="fms-restore-confirm-dialog__conflict-details">
                <div class="fms-restore-confirm-dialog__conflict-name">
                  {{ conflict.name }}
                </div>
                <div class="fms-restore-confirm-dialog__conflict-desc">
                  目标位置已存在同名{{ getTypeLabel(conflict.type) }}
                </div>
              </div>
            </div>

            <div class="fms-restore-confirm-dialog__conflict-actions">
              <el-radio-group v-model="conflict.resolveAction" size="small">
                <el-radio label="skip">跳过</el-radio>
                <el-radio label="rename">重命名</el-radio>
                <el-radio label="replace">替换</el-radio>
              </el-radio-group>

              <el-input
                v-if="conflict.resolveAction === 'rename'"
                v-model="conflict.newName"
                placeholder="输入新名称"
                size="small"
                style="margin-top: 8px; width: 200px"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 恢复选项 -->
      <div class="fms-restore-confirm-dialog__options">
        <div class="fms-restore-confirm-dialog__options-header">
          <span>恢复选项：</span>
        </div>

        <div class="fms-restore-confirm-dialog__options-content">
          <el-checkbox v-model="restoreOptions.preservePermissions">
            保持原有权限设置
          </el-checkbox>
          <el-checkbox v-model="restoreOptions.notifyUsers">
            通知相关用户
          </el-checkbox>
          <el-checkbox v-model="restoreOptions.createBackup">
            恢复前创建备份（如有冲突）
          </el-checkbox>
        </div>
      </div>

      <!-- 恢复进度 -->
      <div v-if="isRestoring" class="fms-restore-confirm-dialog__progress">
        <div class="fms-restore-confirm-dialog__progress-header">
          <span>恢复进度：</span>
        </div>

        <el-progress
          :percentage="restoreProgress"
          :status="restoreStatus"
          :stroke-width="8"
        >
          <template #default="{}">
            <span class="fms-restore-confirm-dialog__progress-text">
              {{ restoreProgressText }}
            </span>
          </template>
        </el-progress>

        <div class="fms-restore-confirm-dialog__progress-details">
          <div
            v-for="item in restoreResults"
            :key="item.id"
            class="fms-restore-confirm-dialog__progress-item"
          >
            <el-icon
              :class="[
                'fms-restore-confirm-dialog__progress-icon',
                `is-${item.status}`,
              ]"
            >
              <component :is="getProgressIcon(item.status)" />
            </el-icon>
            <span class="fms-restore-confirm-dialog__progress-name">
              {{ item.name }}
            </span>
            <span class="fms-restore-confirm-dialog__progress-status">
              {{ getProgressStatusLabel(item.status) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="fms-restore-confirm-dialog__footer">
        <el-button @click="handleClose" :disabled="isRestoring">取消</el-button>
        <el-button
          type="primary"
          :loading="isRestoring"
          @click="handleRestore"
          :disabled="hasUnresolvedConflicts"
        >
          {{ isRestoring ? '恢复中...' : '开始恢复' }}
        </el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import {
    ref,
    reactive,
    computed,
    watch,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import {
    Folder,
    Document,
    Warning,
    Loading,
    Check,
    Close,
  } from '@element-plus/icons-vue'
  import { useRecycleStore } from '../../stores/recycleStore'

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    items: {
      type: Array,
      default: () => [],
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible', 'success'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const recycleStore = useRecycleStore()

  // 响应式数据
  const isRestoring = ref(false)
  const restoreProgress = ref(0)
  const restoreStatus = ref('')
  const restoreProgressText = ref('')
  const restoreResults = ref([])
  const conflictItems = ref([])

  const restoreOptions = reactive({
    preservePermissions: true,
    notifyUsers: false,
    createBackup: true,
  })

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  const hasConflicts = computed(() => {
    return conflictItems.value.length > 0
  })

  const hasUnresolvedConflicts = computed(() => {
    return conflictItems.value.some((item) => !item.resolveAction)
  })

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible) {
        checkConflicts()
        resetProgress()
      }
    }
  )

  // 监听恢复项目变化
  watch(
    () => props.items,
    () => {
      if (props.visible) {
        checkConflicts()
      }
    }
  )

  // 方法
  const checkConflicts = async () => {
    if (!props.items.length) return

    try {
      const conflicts = await recycleStore.checkRestoreConflicts(
        props.items.map((item) => item.id)
      )

      conflictItems.value = conflicts.map((conflict) => ({
        ...conflict,
        resolveAction: '',
        newName: `${conflict.name}_restored`,
      }))
    } catch (error) {
      console.warn('检查恢复冲突失败:', error)
      conflictItems.value = []
    }
  }

  const resetProgress = () => {
    isRestoring.value = false
    restoreProgress.value = 0
    restoreStatus.value = ''
    restoreProgressText.value = ''
    restoreResults.value = []
  }

  const handleRestore = async () => {
    try {
      isRestoring.value = true
      restoreProgress.value = 0
      restoreStatus.value = 'active'
      restoreProgressText.value = '准备恢复...'

      const restoreData = {
        items: props.items.map((item) => ({
          id: item.id,
          originalPath: item.originalPath,
        })),
        conflicts: conflictItems.value.map((conflict) => ({
          id: conflict.id,
          action: conflict.resolveAction,
          newName:
            conflict.resolveAction === 'rename' ? conflict.newName : undefined,
        })),
        options: restoreOptions,
      }

      // 模拟恢复进度
      const totalItems = props.items.length
      restoreResults.value = props.items.map((item) => ({
        id: item.id,
        name: item.name,
        status: 'pending',
      }))

      for (let i = 0; i < totalItems; i++) {
        const item = restoreResults.value[i]
        item.status = 'processing'
        restoreProgressText.value = `正在恢复 ${item.name}...`

        // 模拟恢复延迟
        await new Promise((resolve) => setTimeout(resolve, 1000))

        try {
          // 这里应该调用实际的恢复API
          await recycleStore.restoreItem(props.items[i].id, {
            resolveConflicts: conflictItems.value.filter(
              (c) => c.id === props.items[i].id
            ),
          })

          item.status = 'success'
          restoreProgress.value = Math.round(((i + 1) / totalItems) * 100)
        } catch (error) {
          item.status = 'error'
          item.error = error.message
        }
      }

      const successCount = restoreResults.value.filter(
        (r) => r.status === 'success'
      ).length
      const errorCount = restoreResults.value.filter(
        (r) => r.status === 'error'
      ).length

      if (errorCount === 0) {
        restoreStatus.value = 'success'
        restoreProgressText.value = `恢复完成！成功恢复 ${successCount} 个项目`
        $baseMessage('恢复完成', 'success', 'vab-hey-message-success')
      } else {
        restoreStatus.value = 'exception'
        restoreProgressText.value = `恢复完成！成功 ${successCount} 个，失败 ${errorCount} 个`
        $baseMessage(
          `部分恢复失败：${errorCount} 个项目恢复失败`,
          'warning',
          'vab-hey-message-warning'
        )
      }

      // 延迟关闭对话框
      setTimeout(() => {
        emit('success')
        handleClose()
      }, 2000)
    } catch (error) {
      restoreStatus.value = 'exception'
      restoreProgressText.value = '恢复失败'
      $baseMessage(
        error.message || '恢复失败',
        'error',
        'vab-hey-message-error'
      )
    }
  }

  const handleClose = () => {
    if (!isRestoring.value) {
      dialogVisible.value = false
    }
  }

  // 工具方法
  const getItemIcon = (type) => {
    const iconMap = {
      directory: Folder,
      file: Document,
    }
    return iconMap[type] || Document
  }

  const getStatusTag = (status) => {
    const tagMap = {
      ready: 'success',
      conflict: 'warning',
      error: 'danger',
    }
    return tagMap[status] || 'info'
  }

  const getStatusLabel = (status) => {
    const labelMap = {
      ready: '就绪',
      conflict: '冲突',
      error: '错误',
    }
    return labelMap[status] || status
  }

  const getTypeLabel = (type) => {
    const labelMap = {
      directory: '目录',
      file: '文件',
    }
    return labelMap[type] || type
  }

  const getProgressIcon = (status) => {
    const iconMap = {
      pending: Loading,
      processing: Loading,
      success: Check,
      error: Close,
    }
    return iconMap[status] || Loading
  }

  const getProgressStatusLabel = (status) => {
    const labelMap = {
      pending: '等待中',
      processing: '恢复中',
      success: '成功',
      error: '失败',
    }
    return labelMap[status] || status
  }
</script>

<style lang="scss" scoped>
  .fms-restore-confirm-dialog {
    &__info {
      margin-bottom: 20px;
    }

    &__items {
      margin-bottom: 20px;

      &-header {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 12px;
      }

      &-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid var(--el-border-color-light);
        border-radius: 6px;
      }
    }

    &__item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      &:last-child {
        border-bottom: none;
      }

      &-icon {
        font-size: 16px;
        color: var(--el-color-primary);
        flex-shrink: 0;
      }

      &-info {
        flex: 1;
        min-width: 0;
      }

      &-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 2px;
      }

      &-path {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }

      &-status {
        flex-shrink: 0;
      }
    }

    &__conflicts {
      margin-bottom: 20px;

      &-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: var(--el-color-warning);
        margin-bottom: 12px;
      }

      &-list {
        border: 1px solid var(--el-color-warning-light-7);
        border-radius: 6px;
        background: var(--el-color-warning-light-9);
      }
    }

    &__conflict {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid var(--el-color-warning-light-7);

      &:last-child {
        border-bottom: none;
      }

      &-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
      }

      &-icon {
        font-size: 16px;
        color: var(--el-color-warning);
        flex-shrink: 0;
      }

      &-details {
        flex: 1;
      }

      &-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 2px;
      }

      &-desc {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }

      &-actions {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 8px;
      }
    }

    &__options {
      margin-bottom: 20px;

      &-header {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 12px;
      }

      &-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 12px;
        background: var(--el-bg-color-page);
        border-radius: 6px;
        border: 1px solid var(--el-border-color-light);
      }
    }

    &__progress {
      margin-bottom: 20px;

      &-header {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 12px;
      }

      &-text {
        font-size: 13px;
        color: var(--el-text-color-regular);
      }

      &-details {
        margin-top: 16px;
        max-height: 150px;
        overflow-y: auto;
        border: 1px solid var(--el-border-color-light);
        border-radius: 6px;
      }

      &-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-bottom: 1px solid var(--el-border-color-lighter);

        &:last-child {
          border-bottom: none;
        }
      }

      &-icon {
        font-size: 14px;

        &.is-pending {
          color: var(--el-text-color-placeholder);
        }

        &.is-processing {
          color: var(--el-color-primary);
          animation: rotate 1s linear infinite;
        }

        &.is-success {
          color: var(--el-color-success);
        }

        &.is-error {
          color: var(--el-color-danger);
        }
      }

      &-name {
        flex: 1;
        font-size: 13px;
        color: var(--el-text-color-primary);
      }

      &-status {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }

    &__footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  :deep(.el-radio) {
    margin-right: 16px;
  }
</style>
