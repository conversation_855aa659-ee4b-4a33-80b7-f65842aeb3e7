<template>
  <div class="fms-operation-log-viewer">
    <div class="fms-operation-log-viewer__header">
      <div class="fms-operation-log-viewer__title">
        <el-icon><Document /></el-icon>
        <span>操作日志</span>
      </div>
      <div class="fms-operation-log-viewer__actions">
        <el-button :icon="Download" @click="handleExport">导出日志</el-button>
        <el-button :icon="Refresh" @click="handleRefresh">刷新</el-button>
      </div>
    </div>

    <div class="fms-operation-log-viewer__filters">
      <log-filter
        :values="filters"
        @change="handleFilterChange"
        @reset="handleFilterReset"
      />
    </div>

    <div class="fms-operation-log-viewer__stats">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-statistic title="今日操作" :value="stats.todayCount" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="本周操作" :value="stats.weekCount" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="活跃用户" :value="stats.activeUsers" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="异常操作" :value="stats.errorCount" />
        </el-col>
      </el-row>
    </div>

    <div class="fms-operation-log-viewer__content">
      <el-table
        ref="tableRef"
        :data="logs"
        v-loading="isLoading"
        row-key="id"
        :default-sort="{ prop: 'createdAt', order: 'descending' }"
      >
        <el-table-column
          label="时间"
          width="160"
          sortable="custom"
          prop="createdAt"
        >
          <template #default="{ row }">
            <div class="fms-operation-log-viewer__time">
              <div class="fms-operation-log-viewer__datetime">
                {{ formatDateTime(row.createdAt) }}
              </div>
              <div class="fms-operation-log-viewer__relative">
                {{ getRelativeTime(row.createdAt) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="用户" width="150">
          <template #default="{ row }">
            <div class="fms-operation-log-viewer__user">
              <el-avatar
                v-if="row.user?.avatar"
                :src="row.user.avatar"
                :size="32"
              />
              <el-icon v-else class="fms-operation-log-viewer__user-icon">
                <User />
              </el-icon>
              <div class="fms-operation-log-viewer__user-info">
                <div class="fms-operation-log-viewer__user-name">
                  {{ row.user?.name || '未知用户' }}
                </div>
                <div class="fms-operation-log-viewer__user-ip">
                  {{ row.clientIp }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <div class="fms-operation-log-viewer__action">
              <el-icon class="fms-operation-log-viewer__action-icon">
                <component :is="getActionIcon(row.action)" />
              </el-icon>
              <el-tag :type="getActionTag(row.action)" size="small">
                {{ getActionLabel(row.action) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="目标" min-width="250">
          <template #default="{ row }">
            <div class="fms-operation-log-viewer__target">
              <el-icon class="fms-operation-log-viewer__target-icon">
                <component :is="getTargetIcon(row.targetType)" />
              </el-icon>
              <div class="fms-operation-log-viewer__target-info">
                <div class="fms-operation-log-viewer__target-name">
                  {{ row.targetName || '未知' }}
                </div>
                <div class="fms-operation-log-viewer__target-path">
                  {{ row.targetPath }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="结果" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="详情" width="100" align="center">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleViewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="fms-operation-log-viewer__pagination">
        <pagination
          :current="pagination.current"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          @change="handlePageChange"
        />
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <log-detail-dialog v-model:visible="showDetailDialog" :log="selectedLog" />
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, inject } from 'vue'
  import {
    Document,
    Download,
    Refresh,
    User,
    View,
    Upload,
    Delete,
    Edit,
    Share,
    Folder,
    Document as DocumentIcon,
  } from '@element-plus/icons-vue'
  import { useLogStore } from '../../stores/logStore'
  import LogFilter from './LogFilter.vue'
  import Pagination from '../common/Pagination.vue'
  import LogDetailDialog from './LogDetailDialog.vue'

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const logStore = useLogStore()

  // 响应式数据
  const tableRef = ref()
  const showDetailDialog = ref(false)
  const selectedLog = ref(null)
  const stats = ref({
    todayCount: 0,
    weekCount: 0,
    activeUsers: 0,
    errorCount: 0,
  })

  // 计算属性
  const isLoading = computed(() => logStore.isLoading)
  const logs = computed(() => logStore.logs)
  const pagination = computed(() => logStore.pagination)
  const filters = computed(() => logStore.filters)

  // 方法
  const loadLogs = async () => {
    try {
      await logStore.fetchLogs()
    } catch (error) {
      $baseMessage('加载操作日志失败', 'error', 'vab-hey-message-error')
    }
  }

  const loadStats = async () => {
    try {
      const data = await logStore.fetchLogStats()
      stats.value = data
    } catch (error) {
      console.warn('加载日志统计失败:', error)
    }
  }

  const handleViewDetail = (log) => {
    selectedLog.value = log
    showDetailDialog.value = true
  }

  const handleExport = async () => {
    try {
      const exportData = await logStore.exportLogs(filters.value)

      // 创建下载链接
      const blob = new Blob([exportData], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)

      link.setAttribute('href', url)
      link.setAttribute(
        'download',
        `fms_logs_${new Date().toISOString().split('T')[0]}.csv`
      )
      link.style.visibility = 'hidden'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      $baseMessage('日志导出成功', 'success', 'vab-hey-message-success')
    } catch (error) {
      $baseMessage('日志导出失败', 'error', 'vab-hey-message-error')
    }
  }

  const handleRefresh = () => {
    loadLogs()
    loadStats()
  }

  const handleFilterChange = (newFilters) => {
    logStore.updateFilters(newFilters)
  }

  const handleFilterReset = () => {
    logStore.resetFilters()
  }

  const handlePageChange = (page, pageSize) => {
    logStore.fetchLogs({ page, pageSize })
  }

  // 工具方法
  const getActionIcon = (action) => {
    const iconMap = {
      view: View,
      upload: Upload,
      download: Download,
      delete: Delete,
      edit: Edit,
      share: Share,
      create: Upload,
      move: Edit,
      copy: Document,
    }
    return iconMap[action] || Document
  }

  const getActionTag = (action) => {
    const tagMap = {
      view: 'info',
      upload: 'success',
      download: 'primary',
      delete: 'danger',
      edit: 'warning',
      share: 'success',
      create: 'success',
      move: 'warning',
      copy: 'info',
    }
    return tagMap[action] || 'info'
  }

  const getActionLabel = (action) => {
    const labelMap = {
      view: '查看',
      upload: '上传',
      download: '下载',
      delete: '删除',
      edit: '编辑',
      share: '分享',
      create: '创建',
      move: '移动',
      copy: '复制',
    }
    return labelMap[action] || action
  }

  const getTargetIcon = (targetType) => {
    const iconMap = {
      directory: Folder,
      file: DocumentIcon,
      share: Share,
    }
    return iconMap[targetType] || DocumentIcon
  }

  const getStatusTag = (status) => {
    const tagMap = {
      success: 'success',
      error: 'danger',
      warning: 'warning',
    }
    return tagMap[status] || 'info'
  }

  const getStatusLabel = (status) => {
    const labelMap = {
      success: '成功',
      error: '失败',
      warning: '警告',
    }
    return labelMap[status] || status
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  const getRelativeTime = (dateTime) => {
    if (!dateTime) return ''

    const now = new Date()
    const time = new Date(dateTime)
    const diffMs = now - time
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return '刚刚'
    if (diffMins < 60) return `${diffMins}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays < 7) return `${diffDays}天前`

    return ''
  }

  // 生命周期
  onMounted(() => {
    loadLogs()
    loadStats()
  })
</script>

<style lang="scss" scoped>
  .fms-operation-log-viewer {
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }

    &__title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    &__actions {
      display: flex;
      gap: 8px;
    }

    &__filters {
      margin-bottom: 16px;
    }

    &__stats {
      margin-bottom: 24px;
      padding: 16px;
      background: var(--el-bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__content {
      background: white;
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__time {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    &__datetime {
      font-size: 13px;
      color: var(--el-text-color-primary);
      font-weight: 500;
    }

    &__relative {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
    }

    &__user {
      display: flex;
      align-items: center;
      gap: 8px;

      &-icon {
        font-size: 24px;
        color: var(--el-color-primary);
      }

      &-info {
        flex: 1;
        min-width: 0;
      }

      &-name {
        font-size: 13px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-ip {
        font-size: 12px;
        color: var(--el-text-color-regular);
        font-family: 'Monaco', 'Menlo', monospace;
      }
    }

    &__action {
      display: flex;
      align-items: center;
      gap: 6px;

      &-icon {
        font-size: 14px;
        color: var(--el-color-primary);
      }
    }

    &__target {
      display: flex;
      align-items: center;
      gap: 8px;

      &-icon {
        font-size: 16px;
        color: var(--el-color-primary);
        flex-shrink: 0;
      }

      &-info {
        flex: 1;
        min-width: 0;
      }

      &-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 2px;
      }

      &-path {
        font-size: 12px;
        color: var(--el-text-color-regular);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-family: 'Monaco', 'Menlo', monospace;
      }
    }

    &__pagination {
      padding: 16px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }

  :deep(.el-statistic) {
    text-align: center;

    .el-statistic__head {
      font-size: 13px;
      color: var(--el-text-color-regular);
      margin-bottom: 4px;
    }

    .el-statistic__content {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-color-primary);
    }
  }
</style>
