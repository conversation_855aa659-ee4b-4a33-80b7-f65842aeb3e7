<template>
  <div class="fms-log-filter">
    <VabQueryForm
      :form="filterForm"
      :form-items="formItems"
      :loading="false"
      @query="handleQuery"
      @reset="handleReset"
    >
      <template #querybar>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleQuery">
            查询
          </el-button>
          <el-button :icon="RefreshLeft" @click="handleReset">重置</el-button>
          <el-button :icon="Setting" @click="showAdvanced = !showAdvanced">
            {{ showAdvanced ? '收起' : '高级筛选' }}
          </el-button>
        </el-form-item>
      </template>
    </VabQueryForm>

    <!-- 高级筛选面板 -->
    <el-collapse-transition>
      <div v-show="showAdvanced" class="fms-log-filter__advanced">
        <div class="fms-log-filter__advanced-header">
          <el-icon><Filter /></el-icon>
          <span>高级筛选</span>
        </div>

        <el-form
          :model="advancedFilters"
          label-width="100px"
          class="fms-log-filter__advanced-form"
        >
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="IP地址">
                <el-input
                  v-model="advancedFilters.clientIp"
                  placeholder="输入IP地址"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="用户代理">
                <el-select
                  v-model="advancedFilters.userAgent"
                  placeholder="选择用户代理"
                  clearable
                  filterable
                >
                  <el-option label="Chrome" value="chrome" />
                  <el-option label="Firefox" value="firefox" />
                  <el-option label="Safari" value="safari" />
                  <el-option label="Edge" value="edge" />
                  <el-option label="移动端" value="mobile" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="操作结果">
                <el-select
                  v-model="advancedFilters.status"
                  placeholder="选择操作结果"
                  clearable
                >
                  <el-option label="成功" value="success" />
                  <el-option label="失败" value="error" />
                  <el-option label="警告" value="warning" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="文件大小">
                <div class="fms-log-filter__size-range">
                  <el-input-number
                    v-model="advancedFilters.minSize"
                    :min="0"
                    placeholder="最小"
                    controls-position="right"
                    style="width: 120px"
                  />
                  <span class="fms-log-filter__size-separator">-</span>
                  <el-input-number
                    v-model="advancedFilters.maxSize"
                    :min="0"
                    placeholder="最大"
                    controls-position="right"
                    style="width: 120px"
                  />
                  <el-select
                    v-model="advancedFilters.sizeUnit"
                    style="width: 80px; margin-left: 8px"
                  >
                    <el-option label="B" value="B" />
                    <el-option label="KB" value="KB" />
                    <el-option label="MB" value="MB" />
                    <el-option label="GB" value="GB" />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="操作耗时">
                <div class="fms-log-filter__duration-range">
                  <el-input-number
                    v-model="advancedFilters.minDuration"
                    :min="0"
                    placeholder="最小耗时(ms)"
                    controls-position="right"
                    style="width: 140px"
                  />
                  <span class="fms-log-filter__duration-separator">-</span>
                  <el-input-number
                    v-model="advancedFilters.maxDuration"
                    :min="0"
                    placeholder="最大耗时(ms)"
                    controls-position="right"
                    style="width: 140px"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="标签筛选">
                <el-select
                  v-model="advancedFilters.tags"
                  multiple
                  placeholder="选择标签"
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  style="width: 100%"
                >
                  <el-option
                    v-for="tag in availableTags"
                    :key="tag.id"
                    :label="tag.name"
                    :value="tag.id"
                  >
                    <span style="float: left">{{ tag.name }}</span>
                    <span
                      style="
                        float: right;
                        color: var(--el-text-color-secondary);
                        font-size: 13px;
                      "
                    >
                      {{ tag.count }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item>
                <el-button
                  type="primary"
                  :icon="Search"
                  @click="handleAdvancedQuery"
                >
                  应用高级筛选
                </el-button>
                <el-button :icon="RefreshLeft" @click="handleAdvancedReset">
                  重置高级筛选
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-collapse-transition>

    <!-- 快速筛选标签 -->
    <div v-if="hasActiveFilters" class="fms-log-filter__tags">
      <div class="fms-log-filter__tags-header">
        <span>当前筛选：</span>
      </div>
      <div class="fms-log-filter__tags-content">
        <el-tag
          v-for="tag in activeFilterTags"
          :key="tag.key"
          closable
          @close="removeFilter(tag.key)"
        >
          {{ tag.label }}
        </el-tag>
        <el-button type="text" size="small" @click="clearAllFilters">
          清除所有
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, watch, defineProps, defineEmits } from 'vue'
  import { Search, RefreshLeft, Setting, Filter } from '@element-plus/icons-vue'

  // Props 定义
  const props = defineProps({
    values: {
      type: Object,
      default: () => ({}),
    },
  })

  // Events 定义
  const emit = defineEmits(['change', 'reset'])

  // 响应式数据
  const showAdvanced = ref(false)

  const filterForm = reactive({
    keyword: '',
    action: '',
    targetType: '',
    userId: '',
    dateRange: [],
  })

  const advancedFilters = reactive({
    clientIp: '',
    userAgent: '',
    status: '',
    minSize: null,
    maxSize: null,
    sizeUnit: 'MB',
    minDuration: null,
    maxDuration: null,
    tags: [],
  })

  // 可用标签（模拟数据）
  const availableTags = ref([
    { id: 1, name: '重要操作', count: 156 },
    { id: 2, name: '批量操作', count: 89 },
    { id: 3, name: '异常操作', count: 23 },
    { id: 4, name: '系统操作', count: 45 },
    { id: 5, name: '用户操作', count: 234 },
  ])

  // 表单项配置
  const formItems = [
    {
      prop: 'keyword',
      label: '关键词',
      type: 'input',
      placeholder: '搜索用户、文件名或路径',
      style: { width: '200px' },
    },
    {
      prop: 'action',
      label: '操作类型',
      type: 'select',
      placeholder: '选择操作类型',
      options: [
        { label: '全部', value: '' },
        { label: '查看', value: 'view' },
        { label: '上传', value: 'upload' },
        { label: '下载', value: 'download' },
        { label: '删除', value: 'delete' },
        { label: '编辑', value: 'edit' },
        { label: '分享', value: 'share' },
        { label: '创建', value: 'create' },
        { label: '移动', value: 'move' },
        { label: '复制', value: 'copy' },
      ],
      style: { width: '120px' },
    },
    {
      prop: 'targetType',
      label: '目标类型',
      type: 'select',
      placeholder: '选择目标类型',
      options: [
        { label: '全部', value: '' },
        { label: '目录', value: 'directory' },
        { label: '文件', value: 'file' },
        { label: '分享', value: 'share' },
      ],
      style: { width: '100px' },
    },
    {
      prop: 'userId',
      label: '用户',
      type: 'select',
      placeholder: '选择用户',
      options: [
        { label: '全部用户', value: '' },
        { label: '当前用户', value: 'me' },
        { label: '其他用户', value: 'others' },
      ],
      style: { width: '100px' },
    },
    {
      prop: 'dateRange',
      label: '时间范围',
      type: 'daterange',
      placeholder: ['开始时间', '结束时间'],
      style: { width: '240px' },
    },
  ]

  // 计算属性
  const hasActiveFilters = computed(() => {
    return activeFilterTags.value.length > 0
  })

  const activeFilterTags = computed(() => {
    const tags = []

    if (filterForm.keyword) {
      tags.push({ key: 'keyword', label: `关键词: ${filterForm.keyword}` })
    }

    if (filterForm.action) {
      const actionLabel = formItems
        .find((item) => item.prop === 'action')
        ?.options?.find((opt) => opt.value === filterForm.action)?.label
      tags.push({ key: 'action', label: `操作: ${actionLabel}` })
    }

    if (filterForm.targetType) {
      const typeLabel = formItems
        .find((item) => item.prop === 'targetType')
        ?.options?.find((opt) => opt.value === filterForm.targetType)?.label
      tags.push({ key: 'targetType', label: `类型: ${typeLabel}` })
    }

    if (filterForm.userId) {
      const userLabel = formItems
        .find((item) => item.prop === 'userId')
        ?.options?.find((opt) => opt.value === filterForm.userId)?.label
      tags.push({ key: 'userId', label: `用户: ${userLabel}` })
    }

    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      const startDate = new Date(filterForm.dateRange[0]).toLocaleDateString(
        'zh-CN'
      )
      const endDate = new Date(filterForm.dateRange[1]).toLocaleDateString(
        'zh-CN'
      )
      tags.push({ key: 'dateRange', label: `时间: ${startDate} - ${endDate}` })
    }

    // 高级筛选标签
    if (advancedFilters.clientIp) {
      tags.push({ key: 'clientIp', label: `IP: ${advancedFilters.clientIp}` })
    }

    if (advancedFilters.status) {
      const statusLabels = { success: '成功', error: '失败', warning: '警告' }
      tags.push({
        key: 'status',
        label: `结果: ${statusLabels[advancedFilters.status]}`,
      })
    }

    if (advancedFilters.tags.length > 0) {
      const tagNames = advancedFilters.tags
        .map(
          (tagId) => availableTags.value.find((tag) => tag.id === tagId)?.name
        )
        .filter(Boolean)
        .join(', ')
      tags.push({ key: 'tags', label: `标签: ${tagNames}` })
    }

    return tags
  })

  // 监听props变化
  watch(
    () => props.values,
    (newValues) => {
      Object.assign(filterForm, newValues)
    },
    { immediate: true, deep: true }
  )

  // 方法
  const handleQuery = () => {
    const filters = {
      ...filterForm,
      ...getAdvancedFilters(),
    }
    emit('change', filters)
  }

  const handleReset = () => {
    Object.keys(filterForm).forEach((key) => {
      if (Array.isArray(filterForm[key])) {
        filterForm[key] = []
      } else {
        filterForm[key] = ''
      }
    })

    handleAdvancedReset()
    emit('reset')
  }

  const handleAdvancedQuery = () => {
    handleQuery()
  }

  const handleAdvancedReset = () => {
    Object.keys(advancedFilters).forEach((key) => {
      if (Array.isArray(advancedFilters[key])) {
        advancedFilters[key] = []
      } else if (typeof advancedFilters[key] === 'number') {
        advancedFilters[key] = null
      } else {
        advancedFilters[key] = ''
      }
    })

    advancedFilters.sizeUnit = 'MB'
  }

  const getAdvancedFilters = () => {
    const filters = {}

    Object.keys(advancedFilters).forEach((key) => {
      const value = advancedFilters[key]
      if (value !== '' && value !== null && value !== undefined) {
        if (Array.isArray(value) && value.length > 0) {
          filters[key] = value
        } else if (!Array.isArray(value)) {
          filters[key] = value
        }
      }
    })

    return filters
  }

  const removeFilter = (key) => {
    if (key in filterForm) {
      if (Array.isArray(filterForm[key])) {
        filterForm[key] = []
      } else {
        filterForm[key] = ''
      }
    }

    if (key in advancedFilters) {
      if (Array.isArray(advancedFilters[key])) {
        advancedFilters[key] = []
      } else if (typeof advancedFilters[key] === 'number') {
        advancedFilters[key] = null
      } else {
        advancedFilters[key] = ''
      }
    }

    handleQuery()
  }

  const clearAllFilters = () => {
    handleReset()
  }
</script>

<style lang="scss" scoped>
  .fms-log-filter {
    &__advanced {
      margin-top: 16px;
      padding: 16px;
      background: var(--el-bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);

      &-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 16px;

        .el-icon {
          color: var(--el-color-primary);
        }
      }

      &-form {
        .el-form-item {
          margin-bottom: 16px;
        }
      }
    }

    &__size-range,
    &__duration-range {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    &__size-separator,
    &__duration-separator {
      color: var(--el-text-color-placeholder);
      font-weight: 500;
    }

    &__tags {
      margin-top: 16px;
      padding: 12px;
      background: var(--el-color-info-light-9);
      border-radius: 6px;
      border: 1px solid var(--el-color-info-light-7);

      &-header {
        font-size: 13px;
        color: var(--el-text-color-regular);
        margin-bottom: 8px;
      }

      &-content {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
      }
    }
  }

  :deep(.vab-query-form) {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__label {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }
</style>
