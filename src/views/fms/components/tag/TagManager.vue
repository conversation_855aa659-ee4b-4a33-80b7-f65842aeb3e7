<template>
  <div class="fms-tag-manager">
    <div class="fms-tag-manager__header">
      <div class="fms-tag-manager__title">
        <el-icon><Collection /></el-icon>
        <span>标签管理</span>
      </div>
      <div class="fms-tag-manager__actions">
        <el-button type="primary" :icon="Plus" @click="handleCreate">
          新建标签
        </el-button>
        <el-button
          v-if="selectedTags.length > 0"
          type="danger"
          :icon="Delete"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </div>
    </div>

    <div class="fms-tag-manager__filters">
      <filter-panel
        :fields="filterFields"
        :values="filters"
        @change="handleFilterChange"
        @reset="handleFilterReset"
      />
    </div>

    <div class="fms-tag-manager__stats">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-statistic title="总标签数" :value="stats.total" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="活跃标签" :value="stats.active" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="本月新增" :value="stats.thisMonth" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="使用次数" :value="stats.totalUsage" />
        </el-col>
      </el-row>
    </div>

    <div class="fms-tag-manager__content">
      <el-table
        ref="tableRef"
        :data="tags"
        v-loading="isLoading"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="标签" min-width="200">
          <template #default="{ row }">
            <div class="fms-tag-manager__tag">
              <span
                class="fms-tag-manager__color"
                :style="{ backgroundColor: row.color }"
              />
              <span class="fms-tag-manager__name">{{ row.name }}</span>
              <el-tag v-if="row.category" size="small" type="info">
                {{ row.category }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="描述" prop="description" min-width="150">
          <template #default="{ row }">
            <span class="fms-tag-manager__description">
              {{ row.description || '-' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="使用次数" width="100" align="center">
          <template #default="{ row }">
            <span class="fms-tag-manager__usage">
              {{ row.usageCount || 0 }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'active' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.status === 'active' ? '活跃' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            <span class="fms-tag-manager__time">
              {{ formatDateTime(row.createdAt) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="最后使用" width="160">
          <template #default="{ row }">
            <span class="fms-tag-manager__time">
              {{ formatDateTime(row.lastUsedAt) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="fms-tag-manager__row-actions">
              <el-button type="text" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleToggleStatus(row)"
              >
                {{ row.status === 'active' ? '禁用' : '启用' }}
              </el-button>
              <el-button type="text" size="small" @click="handleViewUsage(row)">
                查看使用
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleDelete(row)"
                class="danger"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="fms-tag-manager__pagination">
        <pagination
          :current="pagination.current"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          @change="handlePageChange"
        />
      </div>
    </div>

    <!-- 创建/编辑标签对话框 -->
    <create-tag-dialog
      v-model:visible="showCreateDialog"
      :tag="editingTag"
      @success="handleCreateSuccess"
    />

    <!-- 标签使用详情对话框 -->
    <tag-usage-dialog v-model:visible="showUsageDialog" :tag="viewingTag" />
  </div>
</template>

<script setup>
  import { ref, computed, watch, onMounted, inject } from 'vue'
  import { Collection, Plus, Delete } from '@element-plus/icons-vue'
  import { useTagStore } from '../../stores/tagStore'
  import FilterPanel from '../common/FilterPanel.vue'
  import Pagination from '../common/Pagination.vue'
  import CreateTagDialog from './CreateTagDialog.vue'
  import TagUsageDialog from './TagUsageDialog.vue'

  // 注入服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // Store
  const tagStore = useTagStore()

  // 响应式数据
  const tableRef = ref()
  const selectedTags = ref([])
  const showCreateDialog = ref(false)
  const showUsageDialog = ref(false)
  const editingTag = ref(null)
  const viewingTag = ref(null)
  const stats = ref({
    total: 0,
    active: 0,
    thisMonth: 0,
    totalUsage: 0,
  })

  // 筛选字段配置
  const filterFields = [
    {
      key: 'keyword',
      label: '关键词',
      type: 'input',
      placeholder: '搜索标签名称或描述',
    },
    {
      key: 'category',
      label: '分类',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '文档', value: 'document' },
        { label: '项目', value: 'project' },
        { label: '部门', value: 'department' },
        { label: '其他', value: 'other' },
      ],
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '活跃', value: 'active' },
        { label: '禁用', value: 'disabled' },
      ],
    },
  ]

  // 计算属性
  const isLoading = computed(() => tagStore.isLoading)
  const tags = computed(() => tagStore.tags)
  const pagination = computed(() => tagStore.pagination)
  const filters = computed(() => tagStore.filters)

  // 监听筛选条件变化
  watch(
    () => tagStore.filters,
    () => {
      loadTags()
    },
    { deep: true }
  )

  // 方法
  const loadTags = async () => {
    try {
      await tagStore.fetchTags()
    } catch (error) {
      $baseMessage('加载标签列表失败', 'error', 'vab-hey-message-error')
    }
  }

  const loadStats = async () => {
    try {
      const data = await tagStore.fetchTagStats()
      stats.value = data
    } catch (error) {
      console.warn('加载标签统计失败:', error)
    }
  }

  const handleCreate = () => {
    editingTag.value = null
    showCreateDialog.value = true
  }

  const handleEdit = (tag) => {
    editingTag.value = tag
    showCreateDialog.value = true
  }

  const handleCreateSuccess = () => {
    loadTags()
    loadStats()
  }

  const handleDelete = (tag) => {
    $baseConfirm(
      `确认删除标签 "${tag.name}"？此操作不可恢复。`,
      () => {},
      async () => {
        try {
          await tagStore.deleteTag(tag.id)
          $baseMessage('标签删除成功', 'success', 'vab-hey-message-success')
          loadStats()
        } catch (error) {
          $baseMessage('标签删除失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handleBatchDelete = () => {
    const tagNames = selectedTags.value.map((tag) => tag.name).join('、')

    $baseConfirm(
      `确认删除选中的 ${selectedTags.value.length} 个标签（${tagNames}）？此操作不可恢复。`,
      () => {},
      async () => {
        try {
          const ids = selectedTags.value.map((tag) => tag.id)
          await tagStore.batchDeleteTags(ids)

          $baseMessage('批量删除成功', 'success', 'vab-hey-message-success')
          selectedTags.value = []
          loadStats()
        } catch (error) {
          $baseMessage('批量删除失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handleToggleStatus = async (tag) => {
    const newStatus = tag.status === 'active' ? 'disabled' : 'active'
    const action = newStatus === 'active' ? '启用' : '禁用'

    try {
      await tagStore.updateTag(tag.id, { status: newStatus })
      $baseMessage(`标签${action}成功`, 'success', 'vab-hey-message-success')
      loadStats()
    } catch (error) {
      $baseMessage(`标签${action}失败`, 'error', 'vab-hey-message-error')
    }
  }

  const handleViewUsage = (tag) => {
    viewingTag.value = tag
    showUsageDialog.value = true
  }

  const handleSelectionChange = (selection) => {
    selectedTags.value = selection
  }

  const handleFilterChange = (newFilters) => {
    tagStore.updateFilters(newFilters)
  }

  const handleFilterReset = () => {
    tagStore.resetFilters()
  }

  const handlePageChange = (page, pageSize) => {
    tagStore.fetchTags({ page, pageSize })
  }

  // 工具方法
  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 生命周期
  onMounted(() => {
    loadTags()
    loadStats()
  })
</script>

<style lang="scss" scoped>
  .fms-tag-manager {
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }

    &__title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    &__actions {
      display: flex;
      gap: 8px;
    }

    &__filters {
      margin-bottom: 16px;
    }

    &__stats {
      margin-bottom: 24px;
      padding: 16px;
      background: var(--el-bg-color-page);
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__content {
      background: white;
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__tag {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    &__color {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      flex-shrink: 0;
      border: 1px solid var(--el-border-color-light);
    }

    &__name {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    &__description {
      color: var(--el-text-color-regular);
      font-size: 13px;
    }

    &__usage {
      font-family: 'Monaco', 'Menlo', monospace;
      color: var(--el-text-color-primary);
      font-weight: 500;
    }

    &__time {
      font-size: 13px;
      color: var(--el-text-color-regular);
    }

    &__row-actions {
      display: flex;
      gap: 4px;

      .danger {
        color: var(--el-color-danger);

        &:hover {
          color: var(--el-color-danger);
        }
      }
    }

    &__pagination {
      padding: 16px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }

  :deep(.el-statistic) {
    text-align: center;

    .el-statistic__head {
      font-size: 13px;
      color: var(--el-text-color-regular);
      margin-bottom: 4px;
    }

    .el-statistic__content {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-color-primary);
    }
  }
</style>
