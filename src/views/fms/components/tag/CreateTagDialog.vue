<template>
  <firefly-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑标签' : '新建标签'"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="标签名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入标签名称"
          maxlength="50"
          show-word-limit
        >
          <template #prefix>
            <el-icon><Collection /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="标签颜色" prop="color">
        <div class="create-tag-dialog__color-picker">
          <el-color-picker
            v-model="formData.color"
            :predefine="predefineColors"
            show-alpha
          />
          <div class="create-tag-dialog__color-preview">
            <el-tag
              :color="formData.color"
              class="create-tag-dialog__preview-tag"
            >
              {{ formData.name || '标签预览' }}
            </el-tag>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="分类" prop="category">
        <el-select
          v-model="formData.category"
          placeholder="选择标签分类"
          filterable
          allow-create
          default-first-option
          style="width: 100%"
        >
          <el-option
            v-for="category in categories"
            :key="category.value"
            :label="category.label"
            :value="category.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入标签描述（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="排序" prop="sortOrder">
        <el-input-number
          v-model="formData.sortOrder"
          :min="0"
          :max="9999"
          placeholder="排序值"
          style="width: 150px"
        />
        <el-text size="small" type="info" style="margin-left: 12px">
          数值越小排序越靠前
        </el-text>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="active">
            <el-icon><Check /></el-icon>
            活跃
          </el-radio>
          <el-radio label="disabled">
            <el-icon><Close /></el-icon>
            禁用
          </el-radio>
        </el-radio-group>
        <div class="create-tag-dialog__help-text">
          <el-text size="small" type="info">
            禁用的标签不会在标签选择器中显示
          </el-text>
        </div>
      </el-form-item>

      <el-form-item label="权限设置">
        <el-checkbox-group v-model="formData.permissions">
          <el-checkbox label="public">公开使用</el-checkbox>
          <el-checkbox label="department">部门内使用</el-checkbox>
          <el-checkbox label="private">仅创建者使用</el-checkbox>
        </el-checkbox-group>
        <div class="create-tag-dialog__help-text">
          <el-text size="small" type="info">控制谁可以使用此标签</el-text>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="create-tag-dialog__footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="isSubmitting" @click="handleSubmit">
          {{ isEdit ? '保存修改' : '创建标签' }}
        </el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import {
    ref,
    reactive,
    computed,
    watch,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import { Collection, Check, Close } from '@element-plus/icons-vue'
  import { useTagStore } from '../../stores/tagStore'

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    tag: {
      type: Object,
      default: null,
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible', 'success'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const tagStore = useTagStore()

  // 响应式数据
  const formRef = ref()
  const isSubmitting = ref(false)

  const formData = reactive({
    name: '',
    color: '#409eff',
    category: '',
    description: '',
    sortOrder: 0,
    status: 'active',
    permissions: ['public'],
  })

  // 预定义颜色
  const predefineColors = [
    '#409eff',
    '#67c23a',
    '#e6a23c',
    '#f56c6c',
    '#909399',
    '#c71585',
    '#ff69b4',
    '#ba55d3',
    '#9370db',
    '#3cb371',
    '#32cd32',
    '#00ced1',
    '#48d1cc',
    '#f0e68c',
    '#dda0dd',
    '#98fb98',
  ]

  // 分类选项
  const categories = [
    { label: '文档', value: 'document' },
    { label: '项目', value: 'project' },
    { label: '部门', value: 'department' },
    { label: '状态', value: 'status' },
    { label: '优先级', value: 'priority' },
    { label: '其他', value: 'other' },
  ]

  // 表单验证规则
  const formRules = {
    name: [
      { required: true, message: '请输入标签名称', trigger: 'blur' },
      {
        min: 1,
        max: 50,
        message: '标签名称长度在 1 到 50 个字符',
        trigger: 'blur',
      },
      { validator: validateTagName, trigger: 'blur' },
    ],
    color: [{ required: true, message: '请选择标签颜色', trigger: 'change' }],
    status: [{ required: true, message: '请选择标签状态', trigger: 'change' }],
    sortOrder: [
      {
        type: 'number',
        min: 0,
        max: 9999,
        message: '排序值范围为 0-9999',
        trigger: 'blur',
      },
    ],
  }

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  const isEdit = computed(() => !!props.tag)

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible) {
        resetForm()
      }
    }
  )

  // 监听标签变化
  watch(
    () => props.tag,
    (tag) => {
      if (tag && props.visible) {
        resetForm()
      }
    }
  )

  // 方法
  const resetForm = () => {
    if (props.tag) {
      // 编辑模式
      formData.name = props.tag.name || ''
      formData.color = props.tag.color || '#409eff'
      formData.category = props.tag.category || ''
      formData.description = props.tag.description || ''
      formData.sortOrder = props.tag.sortOrder || 0
      formData.status = props.tag.status || 'active'
      formData.permissions = props.tag.permissions || ['public']
    } else {
      // 新建模式
      formData.name = ''
      formData.color = '#409eff'
      formData.category = ''
      formData.description = ''
      formData.sortOrder = 0
      formData.status = 'active'
      formData.permissions = ['public']
    }

    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      isSubmitting.value = true

      const submitData = {
        name: formData.name.trim(),
        color: formData.color,
        category: formData.category || null,
        description: formData.description.trim() || null,
        sortOrder: formData.sortOrder,
        status: formData.status,
        permissions: formData.permissions,
      }

      if (isEdit.value) {
        await tagStore.updateTag(props.tag.id, submitData)
        $baseMessage('标签更新成功', 'success', 'vab-hey-message-success')
      } else {
        await tagStore.createTag(submitData)
        $baseMessage('标签创建成功', 'success', 'vab-hey-message-success')
      }

      emit('success')
      handleClose()
    } catch (error) {
      const action = isEdit.value ? '更新' : '创建'
      $baseMessage(
        error.message || `标签${action}失败`,
        'error',
        'vab-hey-message-error'
      )
    } finally {
      isSubmitting.value = false
    }
  }

  const handleClose = () => {
    dialogVisible.value = false
  }

  // 表单验证方法
  async function validateTagName(rule, value, callback) {
    if (!value || !value.trim()) {
      callback(new Error('请输入标签名称'))
      return
    }

    const trimmedValue = value.trim()

    // 检查长度
    if (trimmedValue.length < 1 || trimmedValue.length > 50) {
      callback(new Error('标签名称长度在 1 到 50 个字符'))
      return
    }

    // 检查特殊字符
    const invalidChars = /[<>:"/\\|?*]/
    if (invalidChars.test(trimmedValue)) {
      callback(new Error('标签名称不能包含特殊字符'))
      return
    }

    // 检查是否重复（编辑时排除自身）
    try {
      const existingTags = await tagStore.searchTags(trimmedValue)
      const duplicate = existingTags.find(
        (tag) =>
          tag.name.toLowerCase() === trimmedValue.toLowerCase() &&
          (!isEdit.value || tag.id !== props.tag.id)
      )

      if (duplicate) {
        callback(new Error('标签名称已存在'))
        return
      }
    } catch (error) {
      // 搜索失败时不阻止提交
      console.warn('检查标签重复失败:', error)
    }

    callback()
  }
</script>

<style lang="scss" scoped>
  .create-tag-dialog {
    &__color-picker {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    &__color-preview {
      flex: 1;
    }

    &__preview-tag {
      font-size: 14px;
      padding: 4px 8px;
    }

    &__help-text {
      margin-top: 4px;
    }

    &__footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  :deep(.el-radio) {
    display: flex;
    align-items: center;
    margin-right: 24px;
    margin-bottom: 8px;

    .el-radio__label {
      display: flex;
      align-items: center;
      gap: 4px;
      padding-left: 8px;
    }
  }

  :deep(.el-checkbox) {
    margin-right: 24px;
    margin-bottom: 8px;
  }

  :deep(.el-color-picker) {
    .el-color-picker__trigger {
      width: 40px;
      height: 40px;
      border-radius: 6px;
    }
  }
</style>
