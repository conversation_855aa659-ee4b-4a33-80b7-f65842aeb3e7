<template>
  <div class="fms-tag-selector">
    <el-select
      ref="selectRef"
      v-model="selectedTags"
      :placeholder="placeholder"
      :multiple="multiple"
      :filterable="filterable"
      :remote="remote"
      :remote-method="handleRemoteSearch"
      :loading="isSearching"
      :clearable="clearable"
      :collapse-tags="collapseTags"
      :max-collapse-tags="maxCollapseTags"
      :size="size"
      :disabled="disabled"
      @change="handleChange"
      @visible-change="handleVisibleChange"
      @focus="handleFocus"
      @blur="handleBlur"
    >
      <!-- 分组显示 -->
      <template v-if="groupByCategory && !remote">
        <el-option-group
          v-for="(groupTags, category) in groupedTags"
          :key="category"
          :label="getCategoryLabel(category)"
        >
          <el-option
            v-for="tag in groupTags"
            :key="tag.id"
            :label="tag.name"
            :value="tag.id"
            :disabled="tag.status !== 'active'"
          >
            <div class="fms-tag-selector__option">
              <span
                class="fms-tag-selector__color"
                :style="{ backgroundColor: tag.color }"
              />
              <span class="fms-tag-selector__name">{{ tag.name }}</span>
              <span v-if="showUsageCount" class="fms-tag-selector__usage">
                ({{ tag.usageCount || 0 }})
              </span>
            </div>
          </el-option>
        </el-option-group>
      </template>

      <!-- 普通显示 -->
      <template v-else>
        <el-option
          v-for="tag in displayTags"
          :key="tag.id"
          :label="tag.name"
          :value="tag.id"
          :disabled="tag.status !== 'active'"
        >
          <div class="fms-tag-selector__option">
            <span
              class="fms-tag-selector__color"
              :style="{ backgroundColor: tag.color }"
            />
            <span class="fms-tag-selector__name">{{ tag.name }}</span>
            <span v-if="showUsageCount" class="fms-tag-selector__usage">
              ({{ tag.usageCount || 0 }})
            </span>
          </div>
        </el-option>
      </template>

      <!-- 空状态 -->
      <template #empty>
        <div class="fms-tag-selector__empty">
          <el-empty :image-size="60" description="暂无标签数据">
            <el-button v-if="allowCreate" type="text" @click="handleCreateTag">
              创建新标签
            </el-button>
          </el-empty>
        </div>
      </template>
    </el-select>

    <!-- 已选标签显示 -->
    <div
      v-if="showSelectedTags && selectedTagObjects.length > 0"
      class="fms-tag-selector__selected"
    >
      <div class="fms-tag-selector__selected-title">已选标签：</div>
      <div class="fms-tag-selector__selected-tags">
        <el-tag
          v-for="tag in selectedTagObjects"
          :key="tag.id"
          :color="tag.color"
          :closable="!disabled"
          size="small"
          @close="handleRemoveTag(tag.id)"
        >
          {{ tag.name }}
        </el-tag>
      </div>
    </div>

    <!-- 快速选择 -->
    <div
      v-if="showQuickSelect && quickSelectTags.length > 0"
      class="fms-tag-selector__quick"
    >
      <div class="fms-tag-selector__quick-title">快速选择：</div>
      <div class="fms-tag-selector__quick-tags">
        <el-tag
          v-for="tag in quickSelectTags"
          :key="tag.id"
          :color="tag.color"
          :type="isTagSelected(tag.id) ? '' : 'info'"
          size="small"
          class="fms-tag-selector__quick-tag"
          @click="handleQuickSelect(tag)"
        >
          {{ tag.name }}
        </el-tag>
      </div>
    </div>

    <!-- 创建标签对话框 -->
    <create-tag-dialog
      v-model:visible="showCreateDialog"
      @success="handleCreateSuccess"
    />
  </div>
</template>

<script setup>
  import {
    ref,
    computed,
    watch,
    onMounted,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import { useTagStore } from '../../stores/tagStore'
  import CreateTagDialog from './CreateTagDialog.vue'

  // Props 定义
  const props = defineProps({
    modelValue: {
      type: [Array, String, Number],
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '请选择标签',
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    remote: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    collapseTags: {
      type: Boolean,
      default: true,
    },
    maxCollapseTags: {
      type: Number,
      default: 3,
    },
    size: {
      type: String,
      default: 'default',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    groupByCategory: {
      type: Boolean,
      default: false,
    },
    showUsageCount: {
      type: Boolean,
      default: false,
    },
    showSelectedTags: {
      type: Boolean,
      default: false,
    },
    showQuickSelect: {
      type: Boolean,
      default: false,
    },
    allowCreate: {
      type: Boolean,
      default: false,
    },
    context: {
      type: Object,
      default: () => ({}),
    },
  })

  // Events 定义
  const emit = defineEmits(['update:modelValue', 'change', 'focus', 'blur'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const tagStore = useTagStore()

  // 响应式数据
  const selectRef = ref()
  const isSearching = ref(false)
  const searchKeyword = ref('')
  const searchResults = ref([])
  const showCreateDialog = ref(false)
  const quickSelectTags = ref([])

  // 计算属性
  const selectedTags = computed({
    get: () => {
      if (props.multiple) {
        return Array.isArray(props.modelValue) ? props.modelValue : []
      } else {
        return props.modelValue
      }
    },
    set: (value) => {
      emit('update:modelValue', value)
    },
  })

  const displayTags = computed(() => {
    if (props.remote) {
      return searchResults.value
    } else {
      return tagStore.activeTags
    }
  })

  const groupedTags = computed(() => {
    if (!props.groupByCategory) return {}

    const groups = {}
    displayTags.value.forEach((tag) => {
      const category = tag.category || 'other'
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push(tag)
    })

    return groups
  })

  const selectedTagObjects = computed(() => {
    const selectedIds = Array.isArray(selectedTags.value)
      ? selectedTags.value
      : [selectedTags.value]
    return selectedIds.map((id) => tagStore.tagMap.get(id)).filter(Boolean)
  })

  // 监听选择器显示状态
  watch(
    () => props.modelValue,
    (newValue) => {
      // 确保选中的标签数据已加载
      loadSelectedTagsData(newValue)
    }
  )

  // 方法
  const loadTags = async () => {
    try {
      await tagStore.fetchTags()
    } catch (error) {
      console.warn('加载标签失败:', error)
    }
  }

  const loadSelectedTagsData = async (selectedIds) => {
    if (
      !selectedIds ||
      (Array.isArray(selectedIds) && selectedIds.length === 0)
    ) {
      return
    }

    const ids = Array.isArray(selectedIds) ? selectedIds : [selectedIds]

    // 检查哪些标签数据还未加载
    const missingIds = ids.filter((id) => !tagStore.tagMap.has(id))

    if (missingIds.length > 0) {
      try {
        // 批量获取缺失的标签数据
        const promises = missingIds.map((id) => tagStore.fetchTagDetail(id))
        await Promise.all(promises)
      } catch (error) {
        console.warn('加载选中标签数据失败:', error)
      }
    }
  }

  const loadQuickSelectTags = async () => {
    if (!props.showQuickSelect) return

    try {
      // 获取推荐标签
      const suggestions = await tagStore.getTagSuggestions(props.context)
      quickSelectTags.value = suggestions.slice(0, 10)
    } catch (error) {
      // 如果获取推荐失败，使用热门标签
      quickSelectTags.value = tagStore.popularTags.slice(0, 10)
    }
  }

  const handleRemoteSearch = async (keyword) => {
    if (!keyword) {
      searchResults.value = []
      return
    }

    try {
      isSearching.value = true
      searchKeyword.value = keyword

      const results = await tagStore.searchTags(keyword)
      searchResults.value = results
    } catch (error) {
      console.warn('搜索标签失败:', error)
      searchResults.value = []
    } finally {
      isSearching.value = false
    }
  }

  const handleChange = (value) => {
    emit('change', value)
  }

  const handleVisibleChange = (visible) => {
    if (visible && !props.remote) {
      // 下拉框打开时刷新标签列表
      loadTags()
    }
  }

  const handleFocus = () => {
    emit('focus')
  }

  const handleBlur = () => {
    emit('blur')
  }

  const handleRemoveTag = (tagId) => {
    if (props.multiple) {
      const newValue = selectedTags.value.filter((id) => id !== tagId)
      selectedTags.value = newValue
    } else {
      selectedTags.value = null
    }
  }

  const handleQuickSelect = (tag) => {
    if (props.disabled) return

    if (props.multiple) {
      const currentValue = selectedTags.value || []
      if (currentValue.includes(tag.id)) {
        // 取消选择
        selectedTags.value = currentValue.filter((id) => id !== tag.id)
      } else {
        // 添加选择
        selectedTags.value = [...currentValue, tag.id]
      }
    } else {
      selectedTags.value = tag.id
    }
  }

  const handleCreateTag = () => {
    showCreateDialog.value = true
  }

  const handleCreateSuccess = () => {
    loadTags()
    $baseMessage('标签创建成功', 'success', 'vab-hey-message-success')
  }

  const isTagSelected = (tagId) => {
    if (props.multiple) {
      return selectedTags.value.includes(tagId)
    } else {
      return selectedTags.value === tagId
    }
  }

  const getCategoryLabel = (category) => {
    const categoryMap = {
      document: '文档',
      project: '项目',
      department: '部门',
      status: '状态',
      priority: '优先级',
      other: '其他',
    }
    return categoryMap[category] || category
  }

  // 生命周期
  onMounted(() => {
    if (!props.remote) {
      loadTags()
    }

    loadSelectedTagsData(props.modelValue)
    loadQuickSelectTags()
  })

  // 暴露方法
  defineExpose({
    focus: () => selectRef.value?.focus(),
    blur: () => selectRef.value?.blur(),
  })
</script>

<style lang="scss" scoped>
  .fms-tag-selector {
    &__option {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    &__color {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      flex-shrink: 0;
    }

    &__name {
      flex: 1;
    }

    &__usage {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
    }

    &__empty {
      padding: 20px;
      text-align: center;
    }

    &__selected {
      margin-top: 8px;

      &-title {
        font-size: 13px;
        color: var(--el-text-color-regular);
        margin-bottom: 6px;
      }

      &-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }
    }

    &__quick {
      margin-top: 8px;

      &-title {
        font-size: 13px;
        color: var(--el-text-color-regular);
        margin-bottom: 6px;
      }

      &-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }

      &-tag {
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-select__tags) {
    max-width: 100%;
  }
</style>
