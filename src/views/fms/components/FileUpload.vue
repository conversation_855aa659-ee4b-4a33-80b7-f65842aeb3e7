<template>
  <firefly-dialog
    v-model="visible"
    title="上传文件"
    width="600px"
    @close="handleClose"
  >
    <el-upload
      ref="uploadRef"
      :action="uploadAction"
      :headers="uploadHeaders"
      :data="uploadData"
      :on-progress="handleProgress"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      :before-upload="beforeUpload"
      :auto-upload="false"
      :show-file-list="false"
      multiple
      drag
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">
        将文件拖到此处，或
        <em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip">
          支持多文件上传，单个文件大小不超过 100MB
        </div>
      </template>
    </el-upload>

    <!-- 上传配置表单 -->
    <div v-if="uploadList.length > 0" class="file-upload-config">
      <el-form :model="formData" label-width="80px" size="small">
        <el-form-item label="可见性" prop="visibility">
          <el-radio-group v-model="formData.visibility">
            <el-radio label="public">
              <el-icon><View /></el-icon>
              公开
            </el-radio>
            <el-radio label="department">
              <el-icon><OfficeBuilding /></el-icon>
              部门
            </el-radio>
            <el-radio label="user">
              <el-icon><User /></el-icon>
              用户
            </el-radio>
            <el-radio label="private">
              <el-icon><Hide /></el-icon>
              私有
            </el-radio>
          </el-radio-group>
          <div class="file-upload-config__visibility-help">
            <el-text size="small" type="info">
              公开：所有人可见；部门：仅指定部门内可见；用户：仅指定用户可见；私有：仅创建者可见
            </el-text>
          </div>
        </el-form-item>
        <el-form-item label="文件描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="2"
            placeholder="请输入文件描述（可选）"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <div v-if="uploadList.length > 0" class="file-upload-list">
      <h4>上传列表</h4>
      <div v-for="file in uploadList" :key="file.uid" class="file-upload-item">
        <div class="file-upload-item__info">
          <span class="file-upload-item__name">{{ file.name }}</span>
          <span class="file-upload-item__size">
            {{ formatFileSize(file.size) }}
          </span>
        </div>
        <div class="file-upload-item__progress">
          <el-progress
            :percentage="file.percentage || 0"
            :status="
              file.status === 'success'
                ? 'success'
                : file.status === 'error'
                ? 'exception'
                : ''
            "
          />
        </div>
        <div class="file-upload-item__actions">
          <el-button
            v-if="file.status !== 'success'"
            type="text"
            size="small"
            @click="removeFile(file)"
          >
            移除
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="clearFiles">清空</el-button>
        <el-button
          type="primary"
          @click="startUpload"
          :loading="uploading"
          :disabled="uploadList.length === 0"
        >
          开始上传
        </el-button>
      </span>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import { ref, reactive, watch, computed, inject } from 'vue'
  import {
    UploadFilled,
    View,
    OfficeBuilding,
    User,
    Hide,
  } from '@element-plus/icons-vue'
  import { getToken } from '@/utils/token'
  import { FmsUploadServer } from '@/api/setting'

  const $baseMessage = inject('$baseMessage')

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    directoryId: {
      type: [Number, String],
      default: null,
    },
    visibility: {
      type: String,
      default: 'department',
      validator: (value) =>
        ['public', 'department', 'user', 'private'].includes(value),
    },
    checkDuplicate: {
      type: Boolean,
      default: true,
    },
    description: {
      type: String,
      default: '',
    },
  })

  const emit = defineEmits(['update:modelValue', 'success'])

  const visible = ref(false)
  const uploading = ref(false)
  const uploadRef = ref()
  const uploadList = ref([])

  // 表单数据
  const formData = reactive({
    visibility: props.visibility,
    description: props.description,
  })

  // 上传配置
  const uploadAction = FmsUploadServer
  const uploadHeaders = computed(() => ({
    Authorization: `Bearer ${getToken()}`,
  }))
  const uploadData = computed(() => ({
    directory_id: props.directoryId,
    visibility: formData.visibility,
    description: formData.description,
  }))

  // 监听显示状态
  watch(
    () => props.modelValue,
    (val) => {
      console.log('props.directoryId:', props.directoryId)
      visible.value = val
      if (!val) {
        clearFiles()
        // 重置表单数据
        formData.visibility = props.visibility
        formData.description = props.description
      }
    }
  )

  watch(visible, (val) => {
    emit('update:modelValue', val)
  })

  // 监听props变化，同步到表单数据
  watch(
    () => [props.visibility, props.description],
    ([newVisibility, newDescription]) => {
      formData.visibility = newVisibility
      formData.description = newDescription
    }
  )

  const handleClose = () => {
    visible.value = false
  }

  const handleFileChange = (file, fileList) => {
    // 同步更新uploadList
    uploadList.value = fileList.map((f) => ({
      uid: f.uid,
      name: f.name,
      size: f.size,
      status: f.status || 'ready',
      percentage: f.percentage || 0,
      raw: f.raw || f,
    }))
  }

  const handleFileRemove = (file, fileList) => {
    // 同步更新uploadList
    uploadList.value = fileList.map((f) => ({
      uid: f.uid,
      name: f.name,
      size: f.size,
      status: f.status || 'ready',
      percentage: f.percentage || 0,
      raw: f.raw || f,
    }))
  }

  const beforeUpload = (file) => {
    // 检查文件大小
    const isLt100M = file.size / 1024 / 1024 < 100
    if (!isLt100M) {
      $baseMessage('文件大小不能超过 100MB!', 'error', 'vab-hey-message-error')
      return false
    }
    return true // 允许上传
  }

  const handleProgress = (event, file) => {
    const item = uploadList.value.find((item) => item.uid === file.uid)
    if (item) {
      item.percentage = Math.round(event.percent)
      item.status = 'uploading'
    }
  }

  const handleSuccess = async (response, file) => {
    const item = uploadList.value.find((item) => item.uid === file.uid)
    if (item) {
      item.status = 'success'
      item.percentage = 100
    }

    // 检查是否所有文件都上传完成
    const allSuccess = uploadList.value.every(
      (item) => item.status === 'success'
    )
    if (allSuccess) {
      uploading.value = false
      $baseMessage('所有文件上传成功', 'success', 'vab-hey-message-success')
      emit('success')
      await new Promise((resolve) => setTimeout(resolve, 1000))
      handleClose()
    }
  }

  const handleError = (error, file) => {
    const item = uploadList.value.find((item) => item.uid === file.uid)
    if (item) {
      item.status = 'error'
    }
    uploading.value = false
    $baseMessage(
      '文件上传失败：' + (error.message || '未知错误'),
      'error',
      'vab-hey-message-error'
    )
  }

  const startUpload = () => {
    if (uploadList.value.length === 0) {
      $baseMessage('请先选择文件', 'warning', 'vab-hey-message-warning')
      return
    }

    uploading.value = true
    uploadRef.value?.submit()
  }

  const removeFile = (file) => {
    const index = uploadList.value.findIndex((item) => item.uid === file.uid)
    if (index > -1) {
      uploadList.value.splice(index, 1)
    }
    uploadRef.value?.handleRemove(file.raw)
  }

  const clearFiles = () => {
    uploadList.value = []
    uploadRef.value?.clearFiles()
  }

  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
</script>

<style scoped>
  .file-upload-config {
    margin-top: 20px;
    padding: 16px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 6px;
    border: 1px solid var(--el-border-color-light);
  }

  .file-upload-config__visibility-help {
    margin-top: 8px;
  }

  .file-upload-config :deep(.el-radio) {
    margin-right: 16px;
    margin-bottom: 8px;
  }

  .file-upload-config :deep(.el-radio__label) {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .file-upload-list {
    margin-top: 20px;
  }

  .file-upload-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .file-upload-item__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .file-upload-item__name {
    font-weight: 500;
  }

  .file-upload-item__size {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .file-upload-item__progress {
    flex: 1;
    margin: 0 16px;
  }

  .file-upload-item__actions {
    width: 60px;
    text-align: right;
  }
</style>
