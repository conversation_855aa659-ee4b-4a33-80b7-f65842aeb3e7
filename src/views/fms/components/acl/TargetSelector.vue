<template>
  <div class="fms-target-selector">
    <el-select
      v-model="selectedTarget"
      :placeholder="placeholder"
      :filterable="filterable"
      :remote="remote"
      :remote-method="handleRemoteSearch"
      :loading="isSearching"
      :clearable="clearable"
      :size="size"
      :disabled="disabled"
      @change="handleChange"
      @visible-change="handleVisibleChange"
    >
      <el-option
        v-for="target in displayTargets"
        :key="target.id"
        :label="target.name"
        :value="target.id"
        :disabled="target.disabled"
      >
        <div class="fms-target-selector__option">
          <el-icon class="fms-target-selector__icon">
            <component :is="getTargetIcon(targetType)" />
          </el-icon>
          <div class="fms-target-selector__info">
            <div class="fms-target-selector__name">{{ target.name }}</div>
            <div class="fms-target-selector__path">{{ target.path }}</div>
          </div>
          <div
            v-if="target.size !== undefined"
            class="fms-target-selector__size"
          >
            {{ formatFileSize(target.size) }}
          </div>
        </div>
      </el-option>

      <template #empty>
        <div class="fms-target-selector__empty">
          <el-empty :image-size="60" :description="getEmptyDescription()" />
        </div>
      </template>
    </el-select>

    <!-- 目标路径显示 -->
    <div
      v-if="showPath && selectedTargetInfo"
      class="fms-target-selector__path-display"
    >
      <div class="fms-target-selector__path-label">路径：</div>
      <div class="fms-target-selector__path-value">
        {{ selectedTargetInfo.path }}
      </div>
    </div>

    <!-- 树形选择器 -->
    <div v-if="showTree" class="fms-target-selector__tree">
      <div class="fms-target-selector__tree-header">
        <span>选择{{ getTargetTypeLabel(targetType) }}：</span>
        <el-button
          type="text"
          :icon="Refresh"
          size="small"
          @click="refreshTree"
        >
          刷新
        </el-button>
      </div>

      <div class="fms-target-selector__tree-content">
        <a-tree
          v-if="treeData.length > 0"
          ref="treeRef"
          :tree-data="treeData"
          :height="200"
          :field-names="fieldNames"
          :selected-keys="selectedKeys"
          :expanded-keys="expandedKeys"
          :load-data="loadTreeData"
          show-line
          block-node
          @select="handleTreeSelect"
          @expand="handleTreeExpand"
        >
          <template #title="{ title, isLeaf }">
            <div class="fms-target-selector__tree-node">
              <el-icon class="fms-target-selector__tree-icon">
                <component :is="getTargetIcon(isLeaf ? 'file' : 'directory')" />
              </el-icon>
              <span class="fms-target-selector__tree-title">{{ title }}</span>
            </div>
          </template>
        </a-tree>

        <div v-else class="fms-target-selector__tree-empty">
          <el-empty description="暂无数据" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    computed,
    watch,
    onMounted,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import { Folder, Document, Lock, Refresh } from '@element-plus/icons-vue'
  import { useDirectoryStore } from '../../stores/directoryStore'
  import { useFileStore } from '../../stores/fileStore'
  import { useFmsStore } from '../../stores/fmsStore'

  // Props 定义
  const props = defineProps({
    modelValue: {
      type: [String, Number],
      default: null,
    },
    targetType: {
      type: String,
      required: true,
      validator: (value) => ['directory', 'file'].includes(value),
    },
    placeholder: {
      type: String,
      default: '',
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    remote: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    size: {
      type: String,
      default: 'default',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    showPath: {
      type: Boolean,
      default: true,
    },
    showTree: {
      type: Boolean,
      default: false,
    },
  })

  // Events 定义
  const emit = defineEmits(['update:modelValue', 'change'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const directoryStore = useDirectoryStore()
  const fileStore = useFileStore()
  const fmsStore = useFmsStore()

  // 响应式数据
  const treeRef = ref()
  const isSearching = ref(false)
  const searchKeyword = ref('')
  const searchResults = ref([])
  const targetCache = new Map()
  const treeData = ref([])
  const selectedKeys = ref([])
  const expandedKeys = ref([])

  // 字段映射配置
  const fieldNames = {
    key: 'id',
    title: 'name',
    children: 'children',
  }

  // 计算属性
  const selectedTarget = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  })

  const displayTargets = computed(() => {
    return searchResults.value
  })

  const selectedTargetInfo = computed(() => {
    if (!selectedTarget.value) return null
    return targetCache.get(selectedTarget.value)
  })

  const placeholder = computed(() => {
    if (props.placeholder) return props.placeholder

    const typeMap = {
      directory: '请选择目录',
      file: '请选择文件',
    }
    return typeMap[props.targetType] || '请选择'
  })

  // 监听目标类型变化
  watch(
    () => props.targetType,
    () => {
      // 清空选择和搜索结果
      selectedTarget.value = null
      searchResults.value = []
      searchKeyword.value = ''
      treeData.value = []

      if (props.showTree) {
        loadTreeData()
      }
    }
  )

  // 监听选中值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue && !targetCache.has(newValue)) {
        loadTargetInfo(newValue)
      }

      if (newValue && props.showTree) {
        selectedKeys.value = [String(newValue)]
      }
    }
  )

  // 方法
  const handleRemoteSearch = async (keyword) => {
    if (!keyword) {
      searchResults.value = []
      return
    }

    try {
      isSearching.value = true
      searchKeyword.value = keyword

      const results = await searchTargets(keyword)
      searchResults.value = results

      // 缓存搜索结果
      results.forEach((target) => {
        targetCache.set(target.id, target)
      })
    } catch (error) {
      console.warn('搜索目标失败:', error)
      searchResults.value = []
    } finally {
      isSearching.value = false
    }
  }

  const searchTargets = async (keyword) => {
    try {
      if (props.targetType === 'directory') {
        const response = await directoryStore.fetchDirectories({
          keyword,
          includeStats: true,
        })
        return response.data || []
      } else {
        const response = await fileStore.fetchFiles({
          keyword,
          includeCreator: true,
        })
        return response.data || []
      }
    } catch (error) {
      console.warn('搜索失败:', error)
      return []
    }
  }

  const loadTargetInfo = async (targetId) => {
    if (targetCache.has(targetId)) return

    try {
      let targetInfo

      if (props.targetType === 'directory') {
        targetInfo = await directoryStore.fetchDirectoryDetail(targetId)
      } else {
        targetInfo = await fileStore.fetchFileDetail(targetId)
      }

      targetCache.set(targetId, targetInfo)
    } catch (error) {
      console.warn('加载目标信息失败:', error)
    }
  }

  const loadTreeData = async () => {
    if (props.targetType !== 'directory') return

    try {
      const response = await directoryStore.fetchDirectoryTree({
        includeRoot: true,
      })

      treeData.value = buildTreeData(response.tree || [])

      // 设置初始展开
      if (treeData.value.length > 0) {
        expandedKeys.value = [String(treeData.value[0].id)]
      }
    } catch (error) {
      console.warn('加载目录树失败:', error)
    }
  }

  const buildTreeData = (nodes) => {
    return nodes.map((node) => ({
      ...node,
      key: String(node.id),
      title: node.name,
      isLeaf: node.childrenCount === 0,
      children: node.children ? buildTreeData(node.children) : undefined,
    }))
  }

  const refreshTree = () => {
    loadTreeData()
  }

  const handleTreeSelect = (selectedKeys, { node }) => {
    if (selectedKeys.length > 0) {
      const nodeData = {
        id: Number(node.key),
        name: node.title,
        path: node.path || `/${node.title}`,
        ...node.dataRef,
      }

      selectedTarget.value = nodeData.id
      targetCache.set(nodeData.id, nodeData)

      emit('change', nodeData)
    }
  }

  const handleTreeExpand = (expandedKeys) => {
    expandedKeys.value = expandedKeys
  }

  const handleChange = (value) => {
    const targetInfo = targetCache.get(value)
    emit('change', targetInfo)
  }

  const handleVisibleChange = (visible) => {
    if (visible && !props.remote) {
      // 下拉框打开时的处理
    }
  }

  // 工具方法
  const getTargetIcon = (targetType) => {
    const iconMap = {
      directory: Folder,
      file: Document,
      global: Lock,
    }
    return iconMap[targetType] || Document
  }

  const getTargetTypeLabel = (targetType) => {
    const labelMap = {
      directory: '目录',
      file: '文件',
      global: '全局',
    }
    return labelMap[targetType] || targetType
  }

  const getEmptyDescription = () => {
    const typeMap = {
      directory: '未找到匹配的目录',
      file: '未找到匹配的文件',
    }
    return typeMap[props.targetType] || '暂无数据'
  }

  const formatFileSize = (bytes) => {
    return fmsStore.formatFileSize(bytes)
  }

  // 生命周期
  onMounted(() => {
    if (props.modelValue) {
      loadTargetInfo(props.modelValue)
    }

    if (props.showTree) {
      loadTreeData()
    }
  })
</script>

<style lang="scss" scoped>
  .fms-target-selector {
    &__option {
      display: flex;
      align-items: center;
      gap: 8px;
      width: 100%;
    }

    &__icon {
      font-size: 16px;
      color: var(--el-color-primary);
      flex-shrink: 0;
    }

    &__info {
      flex: 1;
      min-width: 0;
    }

    &__name {
      font-weight: 500;
      color: var(--el-text-color-primary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &__path {
      font-size: 12px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &__size {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
      flex-shrink: 0;
    }

    &__empty {
      padding: 20px;
      text-align: center;
    }

    &__path-display {
      margin-top: 8px;
      padding: 8px 12px;
      background: var(--el-bg-color-page);
      border-radius: 4px;
      border: 1px solid var(--el-border-color-lighter);
      display: flex;
      align-items: center;
      gap: 8px;

      &-label {
        font-size: 13px;
        color: var(--el-text-color-regular);
        flex-shrink: 0;
      }

      &-value {
        font-size: 13px;
        color: var(--el-text-color-primary);
        font-family: 'Monaco', 'Menlo', monospace;
        word-break: break-all;
      }
    }

    &__tree {
      margin-top: 12px;
      border: 1px solid var(--el-border-color-light);
      border-radius: 6px;
      overflow: hidden;

      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        background: var(--el-bg-color-page);
        border-bottom: 1px solid var(--el-border-color-lighter);
        font-size: 14px;
        font-weight: 500;
      }

      &-content {
        padding: 8px;
        max-height: 200px;
        overflow-y: auto;
      }

      &-node {
        display: flex;
        align-items: center;
        gap: 6px;
        width: 100%;
      }

      &-icon {
        font-size: 14px;
        color: var(--el-color-primary);
      }

      &-title {
        font-size: 13px;
        color: var(--el-text-color-primary);
      }

      &-empty {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100px;
      }
    }
  }

  :deep(.el-select) {
    width: 100%;
  }

  // antd-vue 树组件样式覆盖
  :deep(.ant-tree) {
    background: transparent;

    .ant-tree-node-content-wrapper {
      padding: 2px 4px;
      border-radius: 4px;

      &:hover {
        background-color: var(--el-color-primary-light-9);
      }

      &.ant-tree-node-selected {
        background-color: var(--el-color-primary-light-8);
      }
    }

    .ant-tree-title {
      width: 100%;
    }

    .ant-tree-switcher {
      color: var(--el-text-color-regular);

      &:hover {
        background-color: var(--el-fill-color-light);
      }
    }
  }
</style>
