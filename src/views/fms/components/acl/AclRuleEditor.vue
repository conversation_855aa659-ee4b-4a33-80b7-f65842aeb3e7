<template>
  <firefly-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑权限规则' : '添加权限规则'"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="acl-rule-editor">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="主体类型" prop="subjectType">
          <el-radio-group
            v-model="formData.subjectType"
            @change="handleSubjectTypeChange"
          >
            <el-radio label="user">
              <el-icon><User /></el-icon>
              用户
            </el-radio>
            <el-radio label="role">
              <el-icon><UserFilled /></el-icon>
              角色
            </el-radio>
            <el-radio label="department">
              <el-icon><OfficeBuilding /></el-icon>
              部门
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="选择主体" prop="subjectId">
          <subject-selector
            v-model="formData.subjectId"
            :subject-type="formData.subjectType"
            @change="handleSubjectChange"
          />
        </el-form-item>

        <el-form-item label="目标类型" prop="targetType">
          <el-radio-group
            v-model="formData.targetType"
            @change="handleTargetTypeChange"
          >
            <el-radio label="global">
              <el-icon><Lock /></el-icon>
              全局
            </el-radio>
            <el-radio label="directory">
              <el-icon><Folder /></el-icon>
              目录
            </el-radio>
            <el-radio label="file">
              <el-icon><Document /></el-icon>
              文件
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="formData.targetType !== 'global'"
          label="选择目标"
          prop="targetId"
        >
          <target-selector
            v-model="formData.targetId"
            :target-type="formData.targetType"
            @change="handleTargetChange"
          />
        </el-form-item>

        <el-form-item label="权限设置" prop="permissions">
          <div class="acl-rule-editor__permissions">
            <el-checkbox-group
              v-model="selectedPermissions"
              @change="handlePermissionChange"
            >
              <el-checkbox
                v-for="permission in permissionOptions"
                :key="permission.value"
                :label="permission.value"
                :disabled="permission.disabled"
              >
                <div class="acl-rule-editor__permission-option">
                  <el-icon>
                    <component :is="permission.icon" />
                  </el-icon>
                  <span>{{ permission.label }}</span>
                  <span class="acl-rule-editor__permission-desc">
                    {{ permission.description }}
                  </span>
                </div>
              </el-checkbox>
            </el-checkbox-group>

            <div class="acl-rule-editor__permission-preview">
              <span class="acl-rule-editor__permission-label">权限值：</span>
              <el-tag type="info">{{ formData.permissions }}</el-tag>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="生效条件">
          <div class="acl-rule-editor__conditions">
            <div
              v-for="(condition, index) in formData.conditions"
              :key="index"
              class="acl-rule-editor__condition"
            >
              <el-select
                v-model="condition.field"
                placeholder="选择字段"
                style="width: 120px"
              >
                <el-option
                  v-for="field in conditionFields"
                  :key="field.value"
                  :label="field.label"
                  :value="field.value"
                />
              </el-select>

              <el-select
                v-model="condition.operator"
                placeholder="操作符"
                style="width: 100px"
              >
                <el-option
                  v-for="op in getOperatorOptions(condition.field)"
                  :key="op.value"
                  :label="op.label"
                  :value="op.value"
                />
              </el-select>

              <el-input
                v-model="condition.value"
                placeholder="值"
                style="flex: 1; margin: 0 8px"
              />

              <el-button
                type="text"
                :icon="Delete"
                @click="removeCondition(index)"
              />
            </div>

            <el-button type="text" :icon="Plus" @click="addCondition">
              添加条件
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-input-number
            v-model="formData.priority"
            :min="0"
            :max="100"
            placeholder="优先级"
            style="width: 150px"
          />
          <el-text size="small" type="info" style="margin-left: 12px">
            数值越大优先级越高
          </el-text>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="active">
              <el-icon><Check /></el-icon>
              启用
            </el-radio>
            <el-radio label="disabled">
              <el-icon><Close /></el-icon>
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入规则描述（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="acl-rule-editor__footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="isSubmitting" @click="handleSubmit">
          {{ isEdit ? '保存修改' : '创建规则' }}
        </el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import {
    ref,
    reactive,
    computed,
    watch,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import {
    User,
    UserFilled,
    OfficeBuilding,
    Lock,
    Folder,
    Document,
    Plus,
    Delete,
    Check,
    Close,
    View,
    Upload,
    Share,
    Setting,
  } from '@element-plus/icons-vue'
  import { usePermissionStore } from '../../stores/permissionStore'
  import { useFmsPermission } from '@/utils/bmsPermission'
  import SubjectSelector from './SubjectSelector.vue'
  import TargetSelector from './TargetSelector.vue'

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    rule: {
      type: Object,
      default: null,
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible', 'success'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const permissionStore = usePermissionStore()
  const { FMS_PERMISSIONS } = useFmsPermission()

  // 响应式数据
  const formRef = ref()
  const isSubmitting = ref(false)
  const selectedPermissions = ref([])

  const formData = reactive({
    subjectType: 'user',
    subjectId: null,
    targetType: 'global',
    targetId: null,
    permissions: 0,
    conditions: [],
    priority: 0,
    status: 'active',
    description: '',
  })

  // 权限选项
  const permissionOptions = [
    {
      label: '查看',
      value: FMS_PERMISSIONS.VIEW,
      description: '可以查看文件和目录',
      icon: View,
      disabled: false,
    },
    {
      label: '上传',
      value: FMS_PERMISSIONS.UPLOAD,
      description: '可以上传文件到目录',
      icon: Upload,
      disabled: false,
    },
    {
      label: '删除',
      value: FMS_PERMISSIONS.DELETE,
      description: '可以删除文件和目录',
      icon: Delete,
      disabled: false,
    },
    {
      label: '分享',
      value: FMS_PERMISSIONS.SHARE,
      description: '可以创建分享链接',
      icon: Share,
      disabled: false,
    },
    {
      label: '管理',
      value: FMS_PERMISSIONS.MANAGE,
      description: '可以修改文件信息和目录结构',
      icon: Setting,
      disabled: false,
    },
    {
      label: '管理员',
      value: FMS_PERMISSIONS.ADMIN,
      description: '拥有所有权限，包括权限管理',
      icon: Lock,
      disabled: false,
    },
  ]

  // 条件字段选项
  const conditionFields = [
    { label: '时间', value: 'time' },
    { label: 'IP地址', value: 'ip' },
    { label: '文件大小', value: 'fileSize' },
    { label: '文件类型', value: 'fileType' },
    { label: '用户组', value: 'userGroup' },
  ]

  // 表单验证规则
  const formRules = {
    subjectType: [
      { required: true, message: '请选择主体类型', trigger: 'change' },
    ],
    subjectId: [{ required: true, message: '请选择主体', trigger: 'change' }],
    targetType: [
      { required: true, message: '请选择目标类型', trigger: 'change' },
    ],
    targetId: [
      {
        validator: (rule, value, callback) => {
          if (formData.targetType !== 'global' && !value) {
            callback(new Error('请选择目标'))
          } else {
            callback()
          }
        },
        trigger: 'change',
      },
    ],
    permissions: [
      {
        validator: (rule, value, callback) => {
          if (value === 0) {
            callback(new Error('请至少选择一个权限'))
          } else {
            callback()
          }
        },
        trigger: 'change',
      },
    ],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  }

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  const isEdit = computed(() => !!props.rule)

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible) {
        resetForm()
      }
    }
  )

  // 监听规则变化
  watch(
    () => props.rule,
    (rule) => {
      if (rule && props.visible) {
        resetForm()
      }
    }
  )

  // 方法
  const resetForm = () => {
    if (props.rule) {
      // 编辑模式
      formData.subjectType = props.rule.subjectType || 'user'
      formData.subjectId = props.rule.subjectId
      formData.targetType = props.rule.targetType || 'global'
      formData.targetId = props.rule.targetId
      formData.permissions = props.rule.permissions || 0
      formData.conditions = props.rule.conditions || []
      formData.priority = props.rule.priority || 0
      formData.status = props.rule.status || 'active'
      formData.description = props.rule.description || ''

      // 更新权限选择
      updateSelectedPermissions()
    } else {
      // 新建模式
      formData.subjectType = 'user'
      formData.subjectId = null
      formData.targetType = 'global'
      formData.targetId = null
      formData.permissions = 0
      formData.conditions = []
      formData.priority = 0
      formData.status = 'active'
      formData.description = ''

      selectedPermissions.value = []
    }

    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  const updateSelectedPermissions = () => {
    selectedPermissions.value = []
    permissionOptions.forEach((option) => {
      if (formData.permissions & option.value) {
        selectedPermissions.value.push(option.value)
      }
    })
  }

  const handleSubjectTypeChange = () => {
    formData.subjectId = null
  }

  const handleSubjectChange = (subject) => {
    // 主体选择变化时的处理
  }

  const handleTargetTypeChange = () => {
    formData.targetId = null
  }

  const handleTargetChange = (target) => {
    // 目标选择变化时的处理
  }

  const handlePermissionChange = (permissions) => {
    formData.permissions = permissions.reduce(
      (sum, permission) => sum | permission,
      0
    )
  }

  const addCondition = () => {
    formData.conditions.push({
      field: '',
      operator: '',
      value: '',
    })
  }

  const removeCondition = (index) => {
    formData.conditions.splice(index, 1)
  }

  const getOperatorOptions = (field) => {
    const commonOperators = [
      { label: '等于', value: 'eq' },
      { label: '不等于', value: 'ne' },
    ]

    const numberOperators = [
      { label: '大于', value: 'gt' },
      { label: '小于', value: 'lt' },
      { label: '大于等于', value: 'gte' },
      { label: '小于等于', value: 'lte' },
    ]

    const stringOperators = [
      { label: '包含', value: 'contains' },
      { label: '开始于', value: 'startsWith' },
      { label: '结束于', value: 'endsWith' },
    ]

    switch (field) {
      case 'time':
      case 'fileSize':
        return [...commonOperators, ...numberOperators]
      case 'ip':
      case 'fileType':
      case 'userGroup':
        return [...commonOperators, ...stringOperators]
      default:
        return commonOperators
    }
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      isSubmitting.value = true

      const submitData = {
        subjectType: formData.subjectType,
        subjectId: formData.subjectId,
        targetType: formData.targetType,
        targetId: formData.targetType === 'global' ? null : formData.targetId,
        permissions: formData.permissions,
        conditions: formData.conditions.filter(
          (c) => c.field && c.operator && c.value
        ),
        priority: formData.priority,
        status: formData.status,
        description: formData.description.trim() || null,
      }

      if (isEdit.value) {
        await permissionStore.updateRule(props.rule.id, submitData)
        $baseMessage('权限规则更新成功', 'success', 'vab-hey-message-success')
      } else {
        await permissionStore.createRule(submitData)
        $baseMessage('权限规则创建成功', 'success', 'vab-hey-message-success')
      }

      emit('success')
      handleClose()
    } catch (error) {
      const action = isEdit.value ? '更新' : '创建'
      $baseMessage(
        error.message || `权限规则${action}失败`,
        'error',
        'vab-hey-message-error'
      )
    } finally {
      isSubmitting.value = false
    }
  }

  const handleClose = () => {
    dialogVisible.value = false
  }
</script>

<style lang="scss" scoped>
  .acl-rule-editor {
    &__permissions {
      border: 1px solid var(--el-border-color-light);
      border-radius: 6px;
      padding: 16px;
      background: var(--el-bg-color-page);
    }

    &__permission-option {
      display: flex;
      align-items: center;
      gap: 8px;
      width: 100%;

      .el-icon {
        font-size: 14px;
        color: var(--el-color-primary);
      }
    }

    &__permission-desc {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
      margin-left: auto;
    }

    &__permission-preview {
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid var(--el-border-color-lighter);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    &__permission-label {
      font-size: 13px;
      color: var(--el-text-color-regular);
    }

    &__conditions {
      border: 1px solid var(--el-border-color-light);
      border-radius: 6px;
      padding: 16px;
      background: var(--el-bg-color-page);
    }

    &__condition {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &__footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  :deep(.el-radio) {
    display: flex;
    align-items: center;
    margin-right: 24px;
    margin-bottom: 8px;

    .el-radio__label {
      display: flex;
      align-items: center;
      gap: 4px;
      padding-left: 8px;
    }
  }

  :deep(.el-checkbox) {
    display: flex;
    align-items: flex-start;
    margin-right: 0;
    margin-bottom: 12px;
    width: 100%;

    .el-checkbox__label {
      width: 100%;
    }
  }
</style>
