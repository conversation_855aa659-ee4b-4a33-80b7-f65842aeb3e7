<template>
  <div class="fms-subject-selector">
    <el-select
      v-model="selectedSubject"
      :placeholder="placeholder"
      :filterable="filterable"
      :remote="remote"
      :remote-method="handleRemoteSearch"
      :loading="isSearching"
      :clearable="clearable"
      :size="size"
      :disabled="disabled"
      @change="handleChange"
      @visible-change="handleVisibleChange"
    >
      <el-option
        v-for="subject in displaySubjects"
        :key="subject.id"
        :label="subject.name"
        :value="subject.id"
        :disabled="subject.disabled"
      >
        <div class="fms-subject-selector__option">
          <el-avatar v-if="subject.avatar" :src="subject.avatar" :size="24" />
          <el-icon v-else class="fms-subject-selector__icon">
            <component :is="getSubjectIcon(subjectType)" />
          </el-icon>
          <div class="fms-subject-selector__info">
            <div class="fms-subject-selector__name">{{ subject.name }}</div>
            <div class="fms-subject-selector__desc">
              {{ subject.description }}
            </div>
          </div>
          <div v-if="subject.status" class="fms-subject-selector__status">
            <el-tag
              :type="subject.status === 'active' ? 'success' : 'danger'"
              size="small"
            >
              {{ getStatusLabel(subject.status) }}
            </el-tag>
          </div>
        </div>
      </el-option>

      <template #empty>
        <div class="fms-subject-selector__empty">
          <el-empty :image-size="60" :description="getEmptyDescription()" />
        </div>
      </template>
    </el-select>

    <!-- 选中主体信息显示 -->
    <div
      v-if="showSelectedInfo && selectedSubjectInfo"
      class="fms-subject-selector__selected"
    >
      <div class="fms-subject-selector__selected-header">
        <span>已选择：</span>
      </div>
      <div class="fms-subject-selector__selected-content">
        <el-avatar
          v-if="selectedSubjectInfo.avatar"
          :src="selectedSubjectInfo.avatar"
          :size="32"
        />
        <el-icon v-else class="fms-subject-selector__selected-icon">
          <component :is="getSubjectIcon(subjectType)" />
        </el-icon>
        <div class="fms-subject-selector__selected-info">
          <div class="fms-subject-selector__selected-name">
            {{ selectedSubjectInfo.name }}
          </div>
          <div class="fms-subject-selector__selected-desc">
            {{ selectedSubjectInfo.description }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    computed,
    watch,
    onMounted,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import { User, UserFilled, OfficeBuilding } from '@element-plus/icons-vue'

  // Props 定义
  const props = defineProps({
    modelValue: {
      type: [String, Number],
      default: null,
    },
    subjectType: {
      type: String,
      required: true,
      validator: (value) => ['user', 'role', 'department'].includes(value),
    },
    placeholder: {
      type: String,
      default: '',
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    remote: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    size: {
      type: String,
      default: 'default',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    showSelectedInfo: {
      type: Boolean,
      default: false,
    },
  })

  // Events 定义
  const emit = defineEmits(['update:modelValue', 'change'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // 响应式数据
  const isSearching = ref(false)
  const searchKeyword = ref('')
  const searchResults = ref([])
  const subjectCache = new Map()

  // 计算属性
  const selectedSubject = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  })

  const displaySubjects = computed(() => {
    return searchResults.value
  })

  const selectedSubjectInfo = computed(() => {
    if (!selectedSubject.value) return null
    return subjectCache.get(selectedSubject.value)
  })

  const placeholder = computed(() => {
    if (props.placeholder) return props.placeholder

    const typeMap = {
      user: '请选择用户',
      role: '请选择角色',
      department: '请选择部门',
    }
    return typeMap[props.subjectType] || '请选择'
  })

  // 监听主体类型变化
  watch(
    () => props.subjectType,
    () => {
      // 清空选择和搜索结果
      selectedSubject.value = null
      searchResults.value = []
      searchKeyword.value = ''
    }
  )

  // 监听选中值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue && !subjectCache.has(newValue)) {
        loadSubjectInfo(newValue)
      }
    }
  )

  // 方法
  const handleRemoteSearch = async (keyword) => {
    if (!keyword) {
      searchResults.value = []
      return
    }

    try {
      isSearching.value = true
      searchKeyword.value = keyword

      const results = await searchSubjects(keyword)
      searchResults.value = results

      // 缓存搜索结果
      results.forEach((subject) => {
        subjectCache.set(subject.id, subject)
      })
    } catch (error) {
      console.warn('搜索主体失败:', error)
      searchResults.value = []
    } finally {
      isSearching.value = false
    }
  }

  const searchSubjects = async (keyword) => {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 300))

    // 根据主体类型返回不同的模拟数据
    switch (props.subjectType) {
      case 'user':
        return [
          {
            id: 1,
            name: '张三',
            description: '<EMAIL>',
            avatar: null,
            status: 'active',
          },
          {
            id: 2,
            name: '李四',
            description: '<EMAIL>',
            avatar: null,
            status: 'active',
          },
        ].filter(
          (user) =>
            user.name.includes(keyword) || user.description.includes(keyword)
        )

      case 'role':
        return [
          {
            id: 1,
            name: '管理员',
            description: '系统管理员角色',
            status: 'active',
          },
          {
            id: 2,
            name: '普通用户',
            description: '普通用户角色',
            status: 'active',
          },
        ].filter(
          (role) =>
            role.name.includes(keyword) || role.description.includes(keyword)
        )

      case 'department':
        return [
          {
            id: 1,
            name: '技术部',
            description: '负责技术开发和维护',
            status: 'active',
          },
          {
            id: 2,
            name: '市场部',
            description: '负责市场推广和销售',
            status: 'active',
          },
        ].filter(
          (dept) =>
            dept.name.includes(keyword) || dept.description.includes(keyword)
        )

      default:
        return []
    }
  }

  const loadSubjectInfo = async (subjectId) => {
    if (subjectCache.has(subjectId)) return

    try {
      // 模拟API调用获取主体详情
      await new Promise((resolve) => setTimeout(resolve, 200))

      const subjectInfo = {
        id: subjectId,
        name: `${props.subjectType}_${subjectId}`,
        description: `${props.subjectType} description`,
        status: 'active',
      }

      subjectCache.set(subjectId, subjectInfo)
    } catch (error) {
      console.warn('加载主体信息失败:', error)
    }
  }

  const handleChange = (value) => {
    const subjectInfo = subjectCache.get(value)
    emit('change', subjectInfo)
  }

  const handleVisibleChange = (visible) => {
    if (visible && !props.remote) {
      // 下拉框打开时的处理
    }
  }

  // 工具方法
  const getSubjectIcon = (subjectType) => {
    const iconMap = {
      user: User,
      role: UserFilled,
      department: OfficeBuilding,
    }
    return iconMap[subjectType] || User
  }

  const getStatusLabel = (status) => {
    const labelMap = {
      active: '正常',
      disabled: '禁用',
      locked: '锁定',
    }
    return labelMap[status] || status
  }

  const getEmptyDescription = () => {
    const typeMap = {
      user: '未找到匹配的用户',
      role: '未找到匹配的角色',
      department: '未找到匹配的部门',
    }
    return typeMap[props.subjectType] || '暂无数据'
  }

  // 生命周期
  onMounted(() => {
    if (props.modelValue) {
      loadSubjectInfo(props.modelValue)
    }
  })
</script>

<style lang="scss" scoped>
  .fms-subject-selector {
    &__option {
      display: flex;
      align-items: center;
      gap: 8px;
      width: 100%;
    }

    &__icon {
      font-size: 20px;
      color: var(--el-color-primary);
    }

    &__info {
      flex: 1;
      min-width: 0;
    }

    &__name {
      font-weight: 500;
      color: var(--el-text-color-primary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &__desc {
      font-size: 12px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &__status {
      flex-shrink: 0;
    }

    &__empty {
      padding: 20px;
      text-align: center;
    }

    &__selected {
      margin-top: 8px;
      padding: 12px;
      border: 1px solid var(--el-border-color-light);
      border-radius: 6px;
      background: var(--el-bg-color-page);

      &-header {
        font-size: 13px;
        color: var(--el-text-color-regular);
        margin-bottom: 8px;
      }

      &-content {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      &-icon {
        font-size: 24px;
        color: var(--el-color-primary);
      }

      &-info {
        flex: 1;
      }

      &-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 2px;
      }

      &-desc {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
  }

  :deep(.el-select) {
    width: 100%;
  }
</style>
