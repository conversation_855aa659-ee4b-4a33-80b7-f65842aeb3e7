<template>
  <div class="fms-permission-manager">
    <div class="fms-permission-manager__header">
      <div class="fms-permission-manager__title">
        <el-icon><Lock /></el-icon>
        <span>权限管理</span>
      </div>
      <div class="fms-permission-manager__actions">
        <el-button type="primary" :icon="Plus" @click="handleAddRule">
          添加权限规则
        </el-button>
        <el-button :icon="Refresh" @click="handleRefresh">刷新</el-button>
      </div>
    </div>

    <div class="fms-permission-manager__info">
      <el-alert type="info" :closable="false" show-icon>
        <template #title>
          <div class="fms-permission-manager__info-content">
            <div>权限说明：</div>
            <ul>
              <li>
                <strong>查看(1)</strong>
                ：可以查看文件和目录
              </li>
              <li>
                <strong>上传(2)</strong>
                ：可以上传文件到目录
              </li>
              <li>
                <strong>删除(4)</strong>
                ：可以删除文件和目录
              </li>
              <li>
                <strong>分享(8)</strong>
                ：可以创建分享链接
              </li>
              <li>
                <strong>管理(16)</strong>
                ：可以修改文件信息和目录结构
              </li>
              <li>
                <strong>管理员(32)</strong>
                ：拥有所有权限，包括权限管理
              </li>
            </ul>
          </div>
        </template>
      </el-alert>
    </div>

    <div class="fms-permission-manager__content">
      <el-table
        ref="tableRef"
        :data="rules"
        v-loading="isLoading"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="主体" min-width="200">
          <template #default="{ row }">
            <div class="fms-permission-manager__subject">
              <el-icon class="fms-permission-manager__subject-icon">
                <component :is="getSubjectIcon(row.subjectType)" />
              </el-icon>
              <div class="fms-permission-manager__subject-info">
                <div class="fms-permission-manager__subject-name">
                  {{ row.subjectName }}
                </div>
                <div class="fms-permission-manager__subject-type">
                  {{ getSubjectTypeLabel(row.subjectType) }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="目标" min-width="200">
          <template #default="{ row }">
            <div class="fms-permission-manager__target">
              <el-icon class="fms-permission-manager__target-icon">
                <component :is="getTargetIcon(row.targetType)" />
              </el-icon>
              <div class="fms-permission-manager__target-info">
                <div class="fms-permission-manager__target-name">
                  {{ row.targetName || '全局' }}
                </div>
                <div class="fms-permission-manager__target-type">
                  {{ getTargetTypeLabel(row.targetType) }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="权限" width="300">
          <template #default="{ row }">
            <div class="fms-permission-manager__permissions">
              <el-tag
                v-for="permission in getPermissionTags(row.permissions)"
                :key="permission.value"
                :type="permission.type"
                size="small"
                class="fms-permission-manager__permission-tag"
              >
                {{ permission.label }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="条件" width="150">
          <template #default="{ row }">
            <div class="fms-permission-manager__conditions">
              <el-tag
                v-if="row.conditions?.length > 0"
                type="warning"
                size="small"
              >
                {{ row.conditions.length }}个条件
              </el-tag>
              <span v-else class="fms-permission-manager__no-conditions">
                无条件
              </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'active' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            <span class="fms-permission-manager__time">
              {{ formatDateTime(row.createdAt) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="fms-permission-manager__row-actions">
              <el-button type="text" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleToggleStatus(row)"
              >
                {{ row.status === 'active' ? '禁用' : '启用' }}
              </el-button>
              <el-button type="text" size="small" @click="handleTest(row)">
                测试
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="handleDelete(row)"
                class="danger"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="fms-permission-manager__pagination">
        <pagination
          :current="pagination.current"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          @change="handlePageChange"
        />
      </div>
    </div>

    <!-- 权限规则编辑器 -->
    <acl-rule-editor
      v-model:visible="showRuleEditor"
      :rule="editingRule"
      @success="handleRuleSuccess"
    />

    <!-- 权限测试对话框 -->
    <permission-test-dialog
      v-model:visible="showTestDialog"
      :rule="testingRule"
    />
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, inject } from 'vue'
  import {
    Lock,
    Plus,
    Refresh,
    User,
    UserFilled,
    OfficeBuilding,
    Folder,
    Document,
  } from '@element-plus/icons-vue'
  import { usePermissionStore } from '../../stores/permissionStore'
  import { useFmsPermission } from '@/utils/bmsPermission'
  import Pagination from '../common/Pagination.vue'
  import AclRuleEditor from './AclRuleEditor.vue'
  import PermissionTestDialog from './PermissionTestDialog.vue'

  // 注入服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // Store
  const permissionStore = usePermissionStore()
  const { FMS_PERMISSIONS } = useFmsPermission()

  // 响应式数据
  const tableRef = ref()
  const selectedRules = ref([])
  const showRuleEditor = ref(false)
  const showTestDialog = ref(false)
  const editingRule = ref(null)
  const testingRule = ref(null)

  // 计算属性
  const isLoading = computed(() => permissionStore.isLoading)
  const rules = computed(() => permissionStore.rules)
  const pagination = computed(() => permissionStore.pagination)

  // 方法
  const loadRules = async () => {
    try {
      await permissionStore.fetchRules()
    } catch (error) {
      $baseMessage('加载权限规则失败', 'error', 'vab-hey-message-error')
    }
  }

  const handleAddRule = () => {
    editingRule.value = null
    showRuleEditor.value = true
  }

  const handleEdit = (rule) => {
    editingRule.value = rule
    showRuleEditor.value = true
  }

  const handleRuleSuccess = () => {
    loadRules()
  }

  const handleDelete = (rule) => {
    $baseConfirm(
      `确认删除权限规则？此操作不可恢复。`,
      () => {},
      async () => {
        try {
          await permissionStore.deleteRule(rule.id)
          $baseMessage('权限规则删除成功', 'success', 'vab-hey-message-success')
        } catch (error) {
          $baseMessage('权限规则删除失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handleToggleStatus = async (rule) => {
    const newStatus = rule.status === 'active' ? 'disabled' : 'active'
    const action = newStatus === 'active' ? '启用' : '禁用'

    try {
      await permissionStore.updateRule(rule.id, { status: newStatus })
      $baseMessage(
        `权限规则${action}成功`,
        'success',
        'vab-hey-message-success'
      )
    } catch (error) {
      $baseMessage(`权限规则${action}失败`, 'error', 'vab-hey-message-error')
    }
  }

  const handleTest = (rule) => {
    testingRule.value = rule
    showTestDialog.value = true
  }

  const handleRefresh = () => {
    loadRules()
  }

  const handleSelectionChange = (selection) => {
    selectedRules.value = selection
  }

  const handlePageChange = (page, pageSize) => {
    permissionStore.fetchRules({ page, pageSize })
  }

  // 工具方法
  const getSubjectIcon = (subjectType) => {
    const iconMap = {
      user: User,
      role: UserFilled,
      department: OfficeBuilding,
    }
    return iconMap[subjectType] || User
  }

  const getSubjectTypeLabel = (subjectType) => {
    const labelMap = {
      user: '用户',
      role: '角色',
      department: '部门',
    }
    return labelMap[subjectType] || subjectType
  }

  const getTargetIcon = (targetType) => {
    const iconMap = {
      directory: Folder,
      file: Document,
      global: Lock,
    }
    return iconMap[targetType] || Lock
  }

  const getTargetTypeLabel = (targetType) => {
    const labelMap = {
      directory: '目录',
      file: '文件',
      global: '全局',
    }
    return labelMap[targetType] || targetType
  }

  const getPermissionTags = (permissions) => {
    const tags = []

    if (permissions & FMS_PERMISSIONS.VIEW) {
      tags.push({ label: '查看', value: FMS_PERMISSIONS.VIEW, type: 'success' })
    }
    if (permissions & FMS_PERMISSIONS.UPLOAD) {
      tags.push({
        label: '上传',
        value: FMS_PERMISSIONS.UPLOAD,
        type: 'primary',
      })
    }
    if (permissions & FMS_PERMISSIONS.DELETE) {
      tags.push({
        label: '删除',
        value: FMS_PERMISSIONS.DELETE,
        type: 'danger',
      })
    }
    if (permissions & FMS_PERMISSIONS.SHARE) {
      tags.push({
        label: '分享',
        value: FMS_PERMISSIONS.SHARE,
        type: 'warning',
      })
    }
    if (permissions & FMS_PERMISSIONS.MANAGE) {
      tags.push({ label: '管理', value: FMS_PERMISSIONS.MANAGE, type: 'info' })
    }
    if (permissions & FMS_PERMISSIONS.ADMIN) {
      tags.push({ label: '管理员', value: FMS_PERMISSIONS.ADMIN, type: '' })
    }

    return tags
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 生命周期
  onMounted(() => {
    loadRules()
  })
</script>

<style lang="scss" scoped>
  .fms-permission-manager {
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }

    &__title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    &__actions {
      display: flex;
      gap: 8px;
    }

    &__info {
      margin-bottom: 16px;

      &-content {
        font-size: 13px;
        line-height: 1.5;

        ul {
          margin: 8px 0 0 0;
          padding-left: 16px;

          li {
            margin-bottom: 4px;
            color: var(--el-text-color-regular);
          }
        }
      }
    }

    &__content {
      background: white;
      border-radius: 6px;
      border: 1px solid var(--el-border-color-light);
    }

    &__subject,
    &__target {
      display: flex;
      align-items: center;
      gap: 8px;

      &-icon {
        font-size: 16px;
        color: var(--el-color-primary);
      }

      &-info {
        flex: 1;
        min-width: 0;
      }

      &-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-type {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }

    &__permissions {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }

    &__permission-tag {
      margin: 0;
    }

    &__conditions {
      display: flex;
      align-items: center;
    }

    &__no-conditions {
      font-size: 13px;
      color: var(--el-text-color-placeholder);
    }

    &__time {
      font-size: 13px;
      color: var(--el-text-color-regular);
    }

    &__row-actions {
      display: flex;
      gap: 4px;

      .danger {
        color: var(--el-color-danger);

        &:hover {
          color: var(--el-color-danger);
        }
      }
    }

    &__pagination {
      padding: 16px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }
</style>
