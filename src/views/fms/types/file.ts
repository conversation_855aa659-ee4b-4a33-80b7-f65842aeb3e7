/**
 * FMS 文件相关类型定义
 */

import { Visibility, FileCategory, ActionType } from './common'

// 文件基础信息
export interface File {
  id: number
  directoryId: number
  name: string
  originalName: string
  path: string
  size: number
  mimeType: string
  extension: string
  category: FileCategory
  visibility: Visibility
  version: number
  checksum: string
  createdBy: number
  createdAt: string
  updatedAt: string
  deletedAt?: string
  
  // 关联信息
  directory?: {
    id: number
    name: string
    path: string
  }
  creator?: {
    id: number
    name: string
    avatar?: string
  }
  tags?: Array<{
    id: number
    name: string
    color?: string
  }>
  
  // 元数据
  metadata?: Record<string, any>
  
  // 预览信息
  thumbnail?: string
  previewUrl?: string
  downloadUrl?: string
  
  // 权限信息
  permissions?: number
  canView?: boolean
  canDownload?: boolean
  canDelete?: boolean
  canShare?: boolean
  
  // 统计信息
  downloadCount?: number
  shareCount?: number
  viewCount?: number
}

// 文件版本信息
export interface FileVersion {
  id: number
  fileId: number
  version: number
  name: string
  size: number
  path: string
  checksum: string
  createdBy: number
  createdAt: string
  comment?: string
  
  // 关联信息
  file?: File
  creator?: {
    id: number
    name: string
    avatar?: string
  }
}

// 文件上传请求
export interface FileUploadRequest {
  directoryId: number
  file: File | Blob
  name?: string
  visibility?: Visibility
  tags?: number[]
  metadata?: Record<string, any>
  
  // 上传选项
  overwrite?: boolean
  createVersion?: boolean
  comment?: string
}

// 文件上传响应
export interface FileUploadResponse {
  file: File
  version?: FileVersion
}

// 文件更新请求
export interface FileUpdateRequest {
  name?: string
  visibility?: Visibility
  metadata?: Record<string, any>
  tags?: number[]
}

// 文件列表查询参数
export interface FileListParams {
  directoryId?: number
  category?: FileCategory
  visibility?: Visibility
  tags?: number[]
  createdBy?: number
  sizeRange?: [number, number]
  dateRange?: [string, string]
  includeDeleted?: boolean
  includeVersions?: boolean
  includeTags?: boolean
  
  // 通用查询参数
  page?: number
  pageSize?: number
  sort?: string
  order?: 'ASC' | 'DESC'
  search?: string
  searchFields?: string[]
  filter?: Record<string, any>
  op?: Record<string, string>
}

// 文件列表响应
export interface FileListResponse {
  data: File[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 文件下载请求
export interface FileDownloadRequest {
  fileId: number
  version?: number
  inline?: boolean // 是否内联显示
}

// 文件下载响应
export interface FileDownloadResponse {
  url: string
  filename: string
  size: number
  mimeType: string
  expires?: string
}

// 文件预览信息
export interface FilePreviewInfo {
  id: number
  name: string
  size: number
  mimeType: string
  category: FileCategory
  previewUrl?: string
  thumbnailUrl?: string
  downloadUrl?: string
  canPreview: boolean
  previewType: 'image' | 'video' | 'audio' | 'pdf' | 'text' | 'office' | 'none'
}

// 文件版本列表响应
export interface FileVersionListResponse {
  data: FileVersion[]
  total: number
  currentVersion: number
}

// 文件版本回滚请求
export interface FileVersionRevertRequest {
  version: number
  comment?: string
}

// 文件操作历史
export interface FileOperation {
  id: number
  fileId: number
  action: ActionType
  userId: number
  userName: string
  detail: Record<string, any>
  createdAt: string
  
  // 关联信息
  file?: File
  user?: {
    id: number
    name: string
    avatar?: string
  }
}

// 文件统计信息
export interface FileStats {
  totalFiles: number
  totalSize: number
  categoryStats: Array<{
    category: FileCategory
    count: number
    size: number
  }>
  recentUploads: File[]
  popularFiles: File[]
  largestFiles: File[]
}

// 文件搜索结果
export interface FileSearchResult {
  file: File
  highlights: {
    name?: string[]
    content?: string[]
    tags?: string[]
  }
  score: number
}

// 文件批量操作请求
export interface FileBatchOperationRequest {
  fileIds: number[]
  action: 'delete' | 'move' | 'updateVisibility' | 'addTags' | 'removeTags'
  params?: {
    directoryId?: number
    visibility?: Visibility
    tags?: number[]
  }
}

// 文件批量操作响应
export interface FileBatchOperationResponse {
  success: number[]
  failed: Array<{
    id: number
    error: string
  }>
  total: number
}

// 文件上传进度
export interface FileUploadProgress {
  fileId?: number
  filename: string
  size: number
  uploaded: number
  percent: number
  speed: number
  status: 'waiting' | 'uploading' | 'success' | 'error' | 'cancelled'
  error?: string
}

// 文件类型配置
export interface FileTypeConfig {
  extensions: string[]
  mimeTypes: string[]
  category: FileCategory
  maxSize?: number
  allowPreview: boolean
  previewType: string
  icon: string
  color: string
}
