/**
 * FMS 目录相关类型定义
 */

import { Visibility, ActionType } from './common'

// 目录基础信息
export interface Directory {
  id: number
  parentId: number | null
  name: string
  path: string
  visibility: Visibility
  ownerId: number
  ownerType: string
  sortOrder: number
  createdAt: string
  updatedAt: string
  deletedAt?: string
  
  // 关联信息
  owner?: {
    id: number
    name: string
    type: string
  }
  parent?: Directory
  children?: Directory[]
  
  // 统计信息
  childrenCount?: number
  filesCount?: number
  totalSize?: number
  
  // 权限信息
  permissions?: number
  canView?: boolean
  canUpload?: boolean
  canDelete?: boolean
  canShare?: boolean
}

// 目录树节点
export interface DirectoryTreeNode {
  id: number
  parentId: number | null
  name: string
  title: string // 用于 a-tree 显示
  key: string // 用于 a-tree 的 key
  children?: DirectoryTreeNode[]
  isLeaf?: boolean
  disabled?: boolean
  selectable?: boolean
  
  // 扩展属性
  path?: string
  level?: number
  expanded?: boolean
  loading?: boolean
  permissions?: number
}

// 创建目录请求
export interface CreateDirectoryRequest {
  parentId?: number | null
  name: string
  visibility?: Visibility
  sortOrder?: number
}

// 创建目录响应
export interface CreateDirectoryResponse {
  directory: Directory
}

// 重命名目录请求
export interface RenameDirectoryRequest {
  name: string
}

// 移动目录请求
export interface MoveDirectoryRequest {
  newParentId: number | null
}

// 更新目录可见性请求
export interface UpdateDirectoryVisibilityRequest {
  visibility: Visibility
}

// 目录列表查询参数
export interface DirectoryListParams {
  parentId?: number | null
  visibility?: Visibility
  ownerId?: number
  ownerType?: string
  includeDeleted?: boolean
  includeChildren?: boolean
  includeStats?: boolean
  
  // 通用查询参数
  page?: number
  pageSize?: number
  sort?: string
  order?: 'ASC' | 'DESC'
  search?: string
  searchFields?: string[]
  filter?: Record<string, any>
  op?: Record<string, string>
}

// 目录列表响应
export interface DirectoryListResponse {
  data: Directory[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 目录树查询参数
export interface DirectoryTreeParams {
  rootId?: number | null
  maxDepth?: number
  includeFiles?: boolean
  includeDeleted?: boolean
  visibilityFilter?: Visibility[]
}

// 目录树响应
export interface DirectoryTreeResponse {
  tree: DirectoryTreeNode[]
  flatList?: Directory[]
}

// 目录操作历史
export interface DirectoryOperation {
  id: number
  directoryId: number
  action: ActionType
  userId: number
  userName: string
  detail: Record<string, any>
  createdAt: string
  
  // 关联信息
  directory?: Directory
  user?: {
    id: number
    name: string
    avatar?: string
  }
}

// 目录权限检查请求
export interface DirectoryPermissionCheckRequest {
  directoryId: number
  userId?: number
  permission: number
}

// 目录权限检查响应
export interface DirectoryPermissionCheckResponse {
  hasPermission: boolean
  effectivePermissions: number
  inheritedFrom?: {
    type: 'parent' | 'owner' | 'rule'
    id: number
    name: string
  }
}

// 目录统计信息
export interface DirectoryStats {
  totalDirectories: number
  totalFiles: number
  totalSize: number
  publicDirectories: number
  privateDirectories: number
  recentlyCreated: Directory[]
  recentlyModified: Directory[]
}

// 目录排序选项
export interface DirectorySortOption {
  label: string
  value: string
  field: keyof Directory
  order: 'ASC' | 'DESC'
}

// 目录筛选选项
export interface DirectoryFilterOption {
  label: string
  value: any
  count?: number
}

// 目录批量操作请求
export interface DirectoryBatchOperationRequest {
  directoryIds: number[]
  action: 'delete' | 'move' | 'updateVisibility'
  params?: {
    newParentId?: number
    visibility?: Visibility
  }
}

// 目录批量操作响应
export interface DirectoryBatchOperationResponse {
  success: number[]
  failed: Array<{
    id: number
    error: string
  }>
  total: number
}
