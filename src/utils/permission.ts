import { useAclStore } from '@/store/modules/acl'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/views/fms/stores/permissionStore'

/**
 * 是否可以访问目标权限元素
 * @param targetRoleOrPermission 目标(路由|按钮)要求权限
 * @returns {boolean} 满足访问条件
 */
export function hasPermission(targetRoleOrPermission: string[] | GuardType) {
  const { getAdmin, getRole, getPermission } = useAclStore()
  //如需userInfo接口的permissons:["*"]放行全部权限解除注释即可 强烈不建议使用
  //if (getPermission[0] == '*') return true
  if (getAdmin) return true
  if (Array.isArray(targetRoleOrPermission)) {
    return can([...getRole, ...getPermission], {
      permission: targetRoleOrPermission,
      mode: 'oneOf',
    })
  } else {
    const {
      role = [],
      permission = [],
      mode = 'oneOf',
    } = targetRoleOrPermission
    return can([mode !== 'except'], {
      permission: [
        can(getRole, { permission: role, mode }),
        can(getPermission, { permission, mode }),
      ],
      mode,
    })
  }
}

/**
 * FMS 权限检查 - 检查用户对目标资源的权限位
 * @param targetType 目标类型 ('directory' | 'file')
 * @param targetId 目标ID
 * @param permissionBit 权限位
 * @returns {boolean} 是否有权限
 */
export function hasFmsPermission(targetType: 'directory' | 'file', targetId: number, permissionBit: number): boolean {
  const permissionStore = usePermissionStore()
  const key = `${targetType}:${targetId}`
  const permissions = permissionStore.targetPermissions[key]

  if (permissions === undefined) {
    // 如果权限未加载，返回false，组件应该主动加载权限
    return false
  }

  return (permissions & permissionBit) === permissionBit
}

/**
 * FMS 权限检查 - 检查是否有任意一个权限位
 * @param targetType 目标类型
 * @param targetId 目标ID
 * @param permissionBits 权限位数组
 * @returns {boolean} 是否有任意权限
 */
export function hasAnyFmsPermission(targetType: 'directory' | 'file', targetId: number, permissionBits: number[]): boolean {
  return permissionBits.some(bit => hasFmsPermission(targetType, targetId, bit))
}

/**
 * FMS 权限检查 - 检查是否有所有权限位
 * @param targetType 目标类型
 * @param targetId 目标ID
 * @param permissionBits 权限位数组
 * @returns {boolean} 是否有所有权限
 */
export function hasAllFmsPermissions(targetType: 'directory' | 'file', targetId: number, permissionBits: number[]): boolean {
  return permissionBits.every(bit => hasFmsPermission(targetType, targetId, bit))
}

/**
 * 验证指定的角色列表是否有权限
 * @param targetRoles 需要验证的角色列表
 * @returns
 */
export function hasRolePermission(targetRoles: Array<string>) {
  const userStore = useUserStore()
  // 用户拥有的角色
  const { roles } = storeToRefs(userStore)
  for (let i = 0; i < targetRoles.length; i++) {
    if (roles.value.includes(targetRoles[i])) return true
  }
  return false
}

/**
 * 检查是否满足权限
 * @param roleOrPermission 当前用户权限
 * @param target 目标(路由|按钮)要求权限
 * @returns {boolean} 满足访问条件
 */
function can(roleOrPermission: (string | boolean)[], target: CanType): boolean {
  let hasRole = false
  const { permission = [], mode = 'oneOf' } = target
  if (mode === 'allOf')
    hasRole = permission.every((item: string | boolean) =>
      roleOrPermission.includes(item)
    )
  if (mode === 'oneOf')
    hasRole = permission.some((item: string | boolean) =>
      roleOrPermission.includes(item)
    )
  if (mode === 'except')
    hasRole = !permission.every((item: string | boolean) =>
      roleOrPermission.includes(item)
    )
  return hasRole
}
