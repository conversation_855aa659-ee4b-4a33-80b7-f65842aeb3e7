<template>
  <firefly-dialog
    v-model="dialogFormVisible"
    width="466px"
    show-default-button
    :modal="false"
    :close-on-click-modal="false"
    @close="cancelSubmit"
    @confirm="save(true)"
    style="pointer-events: auto"
    :loading="state.loading"
  >
    <template #header>
      <div style="display: flex; align-items: center">
        <span
          style="
            font-size: 16px;
            font-weight: bold;
            line-height: 1em;
            color: #333;
          "
        >
          节点编辑
        </span>
        <el-popover
          v-if="!isEditableRole()"
          placement="top-start"
          :width="350"
          trigger="click"
          content=""
        >
          <template #reference>
            <el-button circle text>
              <vab-icon icon="error-warning-line" />
            </el-button>
          </template>
          <template #default>
            <div class="notes-box">
              <span>
                {{ `部分功能修改受限，如需要修改请联系产品负责人` }}
              </span>
            </div>
          </template>
        </el-popover>
      </div>
    </template>

    <el-form
      ref="formRef"
      :model="state.issueInfo"
      label-width="78px"
      :rules="state.rules"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="节点标题" required prop="subject">
            <el-input
              v-model="state.issueInfo.subject"
              :disabled="
                (!containerId ? false : !saveNode ? true : false) ||
                !isEditableRole()
              "
              placeholder="节点名称"
              :maxlength="18"
              type="text"
              style="width: 320px"
              show-word-limit
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="描述内容" prop="description">
            <el-input
              :disabled="!containerId ? false : !saveNode ? true : false"
              v-model="state.issueInfo.description"
              style="width: 320px"
              :autosize="{ minRows: 2, maxRows: 5 }"
              type="textarea"
              placeholder="请输入内容描述"
              :maxlength="300"
              @change="checkBoxChecked"
              show-word-limit
            />
          </el-form-item>
        </el-col>

        <el-col :span="24" v-if="saveNode">
          <el-form-item label="节点状态">
            <el-select
              v-if="issueStatusList2.length > 0"
              style="width: 200px"
              v-model="state.issueInfo.status_id"
              @change="handleChangeIssueStatus"
            >
              <template #prefix>
                <el-tag
                  v-if="typeof state.issueInfo.status_id != 'undefined'"
                  class="ml-2"
                  :type="getStatusClass(state.issueInfo.status_id) || 'primary'"
                  :style="{ border: 'none' }"
                >
                  <!-- {{ state.issueInfo.issue_status.name ?? '' }} -->
                  {{
                    issueStatusList2.find(
                      (item) => item.id == state.issueInfo.status_id
                    )?.name ?? ''
                  }}
                </el-tag>
              </template>
              <el-option
                v-for="(sItem, sKey) in issueStatusList2"
                :key="sKey"
                :value="sItem.id"
                :label="' '"
                :disabled="sItem.disabled"
              >
                <el-tag
                  class="ml-2"
                  :type="getStatusClass(sItem.id) || 'primary'"
                  :style="{ border: 'none' }"
                >
                  {{ sItem.name }}
                </el-tag>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" v-if="saveNode">
          <el-form-item label="负责人">
            <el-select
              v-model="state.issueInfo.assigned"
              @change="changedrawerForm"
              style="width: 200px"
              filterable
              collapse-tags
              multiple
              placeholder="负责人"
              :disabled="!saveNode ? true : false || !isEditableRole()"
            >
              <el-option
                v-for="item in assignOptions"
                :key="item.user_id"
                :value="item.user_id"
                :label="item.user.lastname + item.user.firstname"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="阶段">
            <el-select
              v-model="state.label.phase.id"
              @change="changedrawerForm"
              filterable
              style="width: 200px"
              clearable
              placeholder="阶段"
              :disabled="!isEditableRole()"
            >
              <el-option
                v-for="item in state.label.phase.options ?? flowNodePhase"
                :key="item.id"
                :value="item.id"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="分类标签">
            <el-select
              v-model="formProperties.label.customLabelIds"
              @change="changedrawerForm"
              clearable
              multiple
              :multiple-limit="2"
              style="width: 200px"
              filterable
              placeholder="分类标签"
              :disabled="!isEditableRole()"
            >
              <el-option
                v-for="item in state.projectLabel"
                :key="item.id"
                :value="item.id"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24" v-if="saveNode">
          <el-form-item label="时间">
            <el-date-picker
              v-model="state.issueInfo.start_date"
              :disabled="!saveNode ? true : false || !isEditableRole()"
              type="date"
              style="width: 128px"
              placeholder="开始日期"
              value-format="YYYY-MM-DD"
              @change="changedrawerForm"
            />
            <span class="text-gray-500">&nbsp;-&nbsp;</span>
            <el-date-picker
              v-model="state.issueInfo.due_date"
              :disabled="!saveNode ? true : false || !isEditableRole()"
              style="width: 128px"
              placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="changedrawerForm"
            />
            <el-tag type="info" size="large" style="margin-left: 6px">
              {{ formProperties?.day_date || '-' }}
            </el-tag>
            天
          </el-form-item>
        </el-col>

        <el-col v-if="saveNode">
          <el-form-item label="完成条件">
            <el-checkbox-group
              v-model="state.issueInfo.custom_fields[0].value"
              @change="checkBoxChecked"
              :disabled="!isEditableRole()"
            >
              <template
                v-for="(item, index) in progressNodeCompletePrecondition"
                :key="index"
              >
                <el-checkbox :label="item" :value="item" />
              </template>
            </el-checkbox-group>
          </el-form-item>
        </el-col>

        <el-col :span="24" v-if="saveNode">
          <el-form-item label="附件">
            <el-upload
              :headers="headers"
              :action="uploadServer"
              :on-success="handleAvatarSuccess"
              :show-file-list="false"
            >
              <el-button class="btn">
                <vab-icon
                  icon="attachments"
                  is-custom-svg
                  class="common-icon"
                />
                上传附件
              </el-button>
            </el-upload>
          </el-form-item>
        </el-col>

        <template
          v-if="
            containerId && currentModel && currentModel.properties.attachment
          "
        >
          <div class="file-list-container" style="margin-left: 70px">
            <FileList
              v-if="currentModel.properties.attachment.length > 0"
              v-model:file-list="currentModel.properties.attachment"
              @on-remove="handleRemove"
              @image-show-status-change="updateEscCauseImg"
              :custom-open-image="true"
              @viewer-image="viewerImage"
            />
          </div>
        </template>

        <el-col
          :span="24"
          v-if="saveNode && currentModel.properties.issue?.child_issue?.length"
        >
          <el-form-item label="子任务">
            <div
              style="
                display: flex;
                flex-wrap: wrap;
                gap: 5px;
                font-weight: bold;
              "
            >
              <template
                v-for="(issue, lIndex) in currentModel.properties.issue
                  .child_issue"
                :key="lIndex"
              >
                <el-tag
                  v-if="issue.subject"
                  type=""
                  size="small"
                  effect="dark"
                  :disable-transitions="false"
                  @click.stop="handleChildIssueClick(issue.parent_id)"
                  :style="{
                    backgroundColor: '#6059f0' + '1a',
                    color: '#6059f0',
                    border: 'none',
                    fontWeight: 'bold',
                    cursor: 'pointer',
                  }"
                  style="margin-right: 0px; margin-left: 0px"
                  class="project-label-custom-tag"
                >
                  {{ issue.subject }}
                </el-tag>
              </template>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </firefly-dialog>
</template>

<script setup>
  import _, { cloneDeep } from 'lodash'
  import { issueStatusClass, priorityIcon } from '@/json/issues'
  import { useUserStore } from '@/store/modules/user'
  import {
    flowNodePhase,
    flowNodeCustomLabel1,
    progressNodeStatusIdToTextMap,
    progressNodeStatusTextToIdMap,
    progressNodeCompletePrecondition,
  } from '~/src/json/product'
  import IssueEditDialog from '@/views/project/issue/components/IssueEditDialog.vue'
  import {
    checkFlowTaskIsFinished,
    checkFlowChildTaskIsFinished,
  } from '@/api/product'
  import { getList } from '@/api/projectLabel'
  import { roleAuth } from '@/utils/auth'

  const userStore = useUserStore()
  const { user_id, token, redmine } = userStore
  const headers = reactive({
    Authorization: `Bearer ${token}`,
  })
  const uploadServer = process.env.VUE_APP_UPLOAD_SERVER

  const NODE_WIDTH = 184
  const NODE_HEIGHT = 42
  const NODE_CONTENT_HEIGHT = 18
  const NODE_LABEL_WIDTH = 36

  const emit = defineEmits(['submitOver', 'cancelSubmit', 'blankClickEvent'])
  const $baseConfirm = inject('$baseConfirm')
  const $baseMessage = inject('$baseMessage')
  const $pub = inject('$pub')

  const formProperties = ref(null)
  const issueEditRef = ref(null)
  const formRef = ref(null)

  const props = defineProps({
    currentModel: {
      type: Object,
      default: () => {},
    },
    assignOptions: {
      type: Array,
      default: () => [],
    },
    containerId: {
      type: Number,
      default: 0,
    },
    // 是否可以保存节点 （即不在模板中编辑）
    saveNode: {
      type: Boolean,
      default: false,
    },
    showDetails: {
      type: Boolean,
      default: false,
    },
    saveNodeBtn: {
      type: Boolean,
      default: false,
    },
    issueStatusList: {
      type: Array,
      default: () => [],
    },
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
  })

  const {
    currentModel,
    assignOptions,
    containerId,
    saveNode,
    showDetails,
    saveNodeBtn,
    issueStatusList,
    // dialogFormVisible,
  } = toRefs(props)

  const dialogFormVisible = ref(props.dialogFormVisible)

  const productId = containerId.value

  const issueStatusList2 = ref(issueStatusList.value)

  const state = reactive({
    loading: false,
    doneStatusList: [39], // 完成事项的状态ID列表
    notStatusList: [37], // 未开始事项的状态ID列表
    assignOptions: [],
    issueInfo: {
      subject: '',
      start_date: '',
      due_date: '',
      status_id: 37,
      custom_fields: [
        {
          value: [],
          customized_type: 'Issue',
          // id: 12,
          name: '',
        },
      ],
    },
    issueInfoOld: {},
    label: {
      phase: {
        id: null,
        name: '',
        keywords: '',
        phase_id: 1,
        color: '#999999',
        options: flowNodePhase,
      },
      customLabels: [
        {
          id: null,
          name: '',
          keywords: '',
          color: '#999999',
          options: flowNodeCustomLabel1,
        },
      ],
      customLabelIds: [],
    },
    oldLabel: {},
    rules: {
      subject: [{ required: true, trigger: 'blur', message: '请输入标题' }],
      // description: [{ required: true, trigger: 'blur', message: '请输入内容' }],
    },
    // 授权角色信息
    auth_role: {
      role: ['Admin'],
    },
    isProductLeader: false,
    projectLabel: [],
  })

  const productInfo = inject('product_info')

  const formInline = reactive({
    user: '',
    region: '',
    date: '',
  })

  const save = async (syncFlowSave = false) => {
    let nowHeight = currentModel.value.properties.height ?? NODE_HEIGHT
    let nowWidth = currentModel.value.properties.width ?? NODE_WIDTH
    let addHeight = 0
    let addWidth = 0

    // 同步保存到数据库情形 状态推进为‘已完成’时进行校验
    if (syncFlowSave) {
      let normallySave = true
      let msg = '节点状态无法设置为完成：'

      let msg2 = await validationCheck()
      if (msg2 !== '') {
        msg += msg2
        normallySave = false
      }

      if (
        state.issueInfoOld.status_id !=
          progressNodeStatusTextToIdMap['已完成'] &&
        state.issueInfo.status_id == progressNodeStatusTextToIdMap['已完成']
      ) {
        let msg3 = await checkChildTaskIsFinished()
        if (msg3 !== '') {
          msg += msg3
          normallySave = false
        }
      }

      // 校验不通过，统一弹出 msg
      if (!normallySave) {
        $baseMessage(msg, 'warning', 'vab-hey-message-warning')
        return
      }

      await checkNodeStatus()
    }

    // 数据校验通过需要保存到数据库时，进行loading
    state.loading = syncFlowSave ? true : false

    // 使用默认高度的情况下，根据内容计算高度
    if (currentModel.value.properties.height == NODE_HEIGHT) {
      if (state.issueInfo.start_date && state.issueInfo.due_date) {
        addHeight += NODE_CONTENT_HEIGHT
      }

      if (state.label?.phase?.id || state.label?.customLabels?.[0]?.id) {
        addHeight += NODE_CONTENT_HEIGHT
      }
    }

    // label更新
    currentModel.value.properties.label = state.label

    // 更新 currentModel 中的字段
    _.forEach(state.issueInfo, (v, k) => {
      if (k !== 'custom_fields') {
        currentModel.value.properties.issue[k] = v
      }
    })

    currentModel.value.properties.issue.custom_fields =
      state.issueInfo.custom_fields

    // 更新标签ID
    currentModel.value.properties.label.customLabelIds =
      formProperties.value.label.customLabelIds
    if (!saveNode.value) {
      currentModel.value.properties.label.customLabels = []
      formProperties.value.label.customLabelIds.forEach((item) => {
        currentModel.value.properties.label.customLabels.push(
          state.projectLabel.find((v) => v.id == item)
        )
      })
    }

    // 在多选标签的情况下或需要自动增长宽度
    let defaultShowTagNum = 3
    let tagCount =
      (currentModel.value.properties.label?.customLabelIds?.length || 0) +
      (currentModel.value.properties.label?.phase?.id ? 1 : 0)
    let extCount = Math.max(tagCount - defaultShowTagNum, 0)
    addWidth = NODE_LABEL_WIDTH * extCount

    currentModel.value.properties.day_date = formProperties.value.day_date
    currentModel.value.properties.status_id = formProperties.value.status_id

    currentModel.value.setHeight(nowHeight + addHeight)
    currentModel.value.setWidth(nowWidth + addWidth)

    emit('submitOver', syncFlowSave)
    if (!syncFlowSave) {
      emit('cancelSubmit')
    }
    if (!saveNode.value) {
      emit('blankClickEvent')
    }
  }

  /**
   * 节点状态设置为已完成前需要校验,将返回一个校验信息
   */
  const validationCheck = async () => {
    let msg = ''
    // 选项1校验
    if (
      state.issueInfo.status_id == progressNodeStatusTextToIdMap['已完成'] &&
      checkBoxSearch('需要上传附件') &&
      !(currentModel.value.properties.attachment?.length > 0)
    ) {
      // state.issueInfo.status_id = state.issueInfoOld.status_id
      msg += `需要上传附件。\n`
    }

    // 选项2校验
    if (
      state.issueInfo.status_id == progressNodeStatusTextToIdMap['已完成'] &&
      checkBoxSearch('需要填写内容')
    ) {
      if (!state.issueInfo.description || state.issueInfo.description == '') {
        msg += `需要填写内容。\n`
      }
      // await formRef.value.validate(async (valid) => {
      //   console.log('校验结果:', valid)
      //   msg += !valid ? `需要填写内容。\n` : '' // 存入提示信息
      //   return valid
      // })
    }

    // // 选项3校验 20250315 考虑到关联事项关系暂无区分主从关系，检测逻辑待定
    // if (checkBoxSearch('关联任务需先完成')) {
    //   const { data } = await checkFlowTaskIsFinished({
    //     ids: [currentModel.value.properties.issue_id],
    //   })

    //   let relationProgressIssue = data
    //   if (relationProgressIssue?.length > 0) {
    //     msg += `关联任务需先完成。\n` // 存入提示信息
    //   }
    // }

    return msg
  }

  const checkChildTaskIsFinished = async () => {
    let msg = ''
    const { data } = await checkFlowChildTaskIsFinished({
      ids: [currentModel.value.properties.issue_id],
    })

    if (data?.length > 0) {
      msg += `子任务需先完成。\n` // 存入提示信息
    }

    return msg
  }

  const cancelSubmit = () => {
    // 恢复原始数据
    state.issueInfo = cloneDeep(state.issueInfoOld)
    state.label = cloneDeep(state.oldLabel)
    formProperties.value.label.customLabelIds = cloneDeep(
      state.oldLabel.customLabelIds
    )
    state.issueInfo.custom_fields[0].value = cloneDeep(
      state.issueInfoOld.custom_fields[0].value
    )
    save(false)
  }

  /**
   * 往后节点状态的检查：当前节点设置为完成或者关闭时，往后节点状态自动设置为进行中
   */
  const checkNodeStatus = async () => {
    let newStatusId = state.issueInfo.status_id
    let oldStatusId = state.issueInfoOld.status_id

    let nextNodes = [] // 往后的下一级节点
    let siblingNodes = [] // 同级的节点

    if (
      progressNodeStatusIdToTextMap[oldStatusId] != '已完成' &&
      progressNodeStatusIdToTextMap[newStatusId] == '已完成'
    ) {
      nextNodes = currentModel.value.graphModel.getNodeOutgoingNode(
        currentModel.value.id
      )
      siblingNodes = currentModel.value.graphModel.getNodeIncomingNode(
        nextNodes[0]?.id
      )

      let siblingNodesAllDone = true
      for (let i = 0; i < siblingNodes.length; i++) {
        if (
          siblingNodes[i].id !== currentModel.value.id &&
          progressNodeStatusIdToTextMap[
            siblingNodes[i].properties.issue.status_id
          ] != '已完成'
        ) {
          siblingNodesAllDone = false
          break
        }
      }

      // if (siblingNodesAllDone) {
      //   nextNodes.forEach((node) => {
      //     // 使用 setProperties 方法更新属性
      //     node.setProperties({
      //       status_id: progressNodeStatusTextToIdMap['进行中'],
      //       issue: {
      //         ...node.properties.issue, // 保留原有属性
      //         status_id: progressNodeStatusTextToIdMap['进行中'],
      //       },
      //     })
      //   })
      // }
      if (siblingNodesAllDone) {
        nextNodes.forEach((node) => {
          // 只有当下一级节点是"未开始"状态时才更新
          if (
            progressNodeStatusIdToTextMap[node.properties.issue.status_id] ==
            '未开始'
          ) {
            node.setProperties({
              status_id: progressNodeStatusTextToIdMap['进行中'],
              issue: {
                ...node.properties.issue,
                status_id: progressNodeStatusTextToIdMap['进行中'],
              },
            })
          }
        })
      }
    }
  }

  let customFields = ref([])
  const initData = async () => {
    state.loading = false
    formProperties.value = cloneDeep(props.currentModel.properties)

    const {
      subject,
      start_date,
      due_date,
      status_id,
      assigned_to_id,
      issue_status,
      custom_fields, //
      description,
      assigned,
    } = props.currentModel.properties.issue

    // 20250313 custom_fields拿到为分散各项，需要聚合一下
    // 计算 custom_fields 进行聚合
    let custom_fields_copy = cloneDeep(custom_fields)
    const formattedCustomFields = async () => {
      const rawCustomFields = custom_fields_copy || []
      const groupedFields = {}

      rawCustomFields.forEach((field) => {
        const fieldId = field.id

        if (!groupedFields[fieldId]) {
          groupedFields[fieldId] = {
            id: fieldId,
            name: field?.field?.name ?? '',
            value: [],
          }
        }

        if (field.value) {
          let values = Array.isArray(field.value)
            ? field.value.flat()
            : [field.value]
          values = values.filter((v) => v !== undefined && v !== null) // 过滤 null/undefined
          groupedFields[fieldId].value.push(...values)
        }
      })

      return Object.values(groupedFields)
    }

    // customLabelIds 更新
    if (!saveNode.value) {
      if (
        !state.label.customLabelIds ||
        state.label.customLabelIds.length == 0
      ) {
        state.label.customLabelIds = []
        state.label.customLabels.forEach((item) => {
          if (item.id) {
            state.label.customLabelIds.push(item.id)
          }
        })
        formProperties.value.label.customLabelIds = state.label.customLabelIds
      }
    }

    state.label = cloneDeep(props.currentModel.properties.label) ?? state.label
    state.oldLabel = cloneDeep(state.label)

    customFields.value = await formattedCustomFields()

    // console.log('init data subject:', subject)

    state.issueInfo = {
      subject,
      start_date,
      due_date,
      status_id,
      assigned_to_id,
      issue_status,
      custom_fields: customFields.value,
      description: description ?? '',
      assigned,
    }

    state.issueInfoOld = {
      subject,
      start_date,
      due_date,
      status_id,
      assigned_to_id,
      issue_status,
      custom_fields: customFields.value,
      description: description ?? '',
      assigned,
    }

    state.assignOptions = props.assignOptions
    getLabelList()

    await new Promise((resolve) => setTimeout(resolve, 500))
    checkBoxChecked()
  }

  const getStatusClass = (v) => {
    return issueStatusClass[v] || ''
  }

  const changedrawerForm = () => {
    formProperties.value = upProperties(formProperties.value)
  }

  const handleChangeIssueStatus = async (selectedStatusId) => {
    // if (
    //   selectedStatusId == progressNodeStatusTextToIdMap['已完成'] &&
    //   customFields.value
    // ) {
    //   let preconditionValues = customFields.value[0].value
    //   // preconditionValues.forEach((item) => {
    //   //   switch (item) {
    //   //     case '需要上传附件': {
    //   //       if (!(currentModel.value.properties.attachment?.length > 0)) {
    //   //         state.issueInfo.status_id = state.issueInfoOld.status_id
    //   //         $baseMessage(`需要上传附件`, 'warning', 'vab-hey-message-warning')
    //   //       }
    //   //       break
    //   //     }
    //   //     case '需要填写内容': {
    //   //       break
    //   //     }
    //   //     case '关联任务需先完成': {
    //   //       break
    //   //     }
    //   //   }
    //   // })
    //   console.log('preconditionValues:', preconditionValues)
    // }
  }

  /**
   * checkbox是否勾选了，看是否禁用某些option item
   */
  const checkBoxChecked = async () => {
    let msg = await validationCheck()
    if (msg !== '') {
      issueStatusList2.value.forEach((item) => {
        if (item.name == '已完成') {
          item.disabled = true
        }
      })
    } else {
      issueStatusList2.value.forEach((item) => {
        if (item.name == '已完成') {
          item.disabled = false
        }
      })
    }
  }

  // watch(
  //   () => state.issueInfo.subject,
  //   () => {
  //     // console.log('state.issueInfo.subject changed:', state.issueInfo.subject)
  //   }
  // )

  /**
   * 检查checkbox是否有勾选某个值
   */
  const checkBoxSearch = (str) => {
    return (
      state.issueInfo.custom_fields?.[0]?.value == str ||
      state.issueInfo.custom_fields?.[0]?.value.includes(str)
    )
  }

  /**
   * 更新node节点状态
   * @param {*} node
   */
  const upProperties = (properties) => {
    // console.log('1235upProperties properties:', properties)
    // 计算开始时间
    if (state.issueInfo.start_date) {
      let diffTime = 0,
        effeTime = 0
      if (state.issueInfo.due_date) {
        const stDate = new Date(state.issueInfo.start_date)
        const enDate = new Date(state.issueInfo.due_date)
        const nowDate = new Date()
        // 工作天
        diffTime = Math.abs(enDate - stDate)
        // 超时天
        effeTime = ((enDate - nowDate) / 1000 / 86400).toFixed(0)
      }

      if (effeTime < 0) {
        // 2.1 超时，延时
        properties.status = 4
      } else {
        // 2.2未超时
        if (state.issueInfo.status_id == 1 || state.issueInfo.status_id == 10) {
          // 2.2.1 未超时未开始
          properties.status = 3
        } else {
          // 2.2.2 未超时已开始
          properties.status = 2
        }
      }
      diffTime = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      properties.day_date = diffTime > 0 ? diffTime : '-'
    } else {
      // 未开始
      properties.status = 3
    }

    if (state.doneStatusList.indexOf(state.issueInfo.status_id) > -1) {
      // 1.状态为完成或者关闭时，节点对应也完成
      properties.status = 1
    } else if (
      // 2. 未开始状态
      state.notStatusList.indexOf(state.issueInfo.status_id) > -1
    ) {
      properties.status = 3
    }
    return properties
  }

  /**
   * 处理上传成功
   */
  const handleAvatarSuccess = async (res) => {
    if (currentModel.value) {
      if (
        typeof currentModel.value.properties.attachment == 'undefined' ||
        !currentModel.value.properties.attachment
      ) {
        currentModel.value.properties.attachment = []
      }
      let list = [
        ...currentModel.value.properties.attachment,
        {
          filename: res.data.upload.attachment.filename,
          url: res.data.upload.attachment.url,
          id: res.data.upload.attachment.id,
        },
      ]
      currentModel.value.properties.attachment = []
      currentModel.value.properties.attachment = list
    }
  }

  /**
   * 获取标签列表
   */
  const getLabelList = async () => {
    if (!currentModel.value.properties.issue_id) {
      state.projectLabel = []
      _.forEach(flowNodeCustomLabel1, (value, key) => {
        state.projectLabel.push({
          id: value.id,
          name: value.keywords,
          color: value.color,
          text_color: value.color,
          options: flowNodeCustomLabel1,
        })
      })
      return
    }

    let queryForm = {
      pageNo: 1,
      limit: 1000,
      title: '',
      filter: { project_id: productId },
      op: { name: 'LIKE' },
      sort: 'id',
      order: 'ASC',
    }

    const { data } = await getList(queryForm)

    state.projectLabel = data.data

    if (!data.data || data.data.length == 0) {
      _.forEach(flowNodeCustomLabel1, (value, key) => {
        state.projectLabel.push({
          id: value.id,
          name: value.keywords,
          color: value.color,
          text_color: value.color,
          options: flowNodeCustomLabel1,
        })
      })
    }
  }

  /**
   * 子任务点击
   */
  const handleChildIssueClick = (issueId) => {
    $pub('progress-node-click-task-count-tag', issueId)
  }

  /**
   * 是否为可编辑角色，目前：超管、产品负责人
   */
  const isEditableRole = () => {
    return state.isProductLeader || roleAuth(state.auth_role.role)
  }

  watch(
    () => props.currentModel,
    () => {
      // console.log('model change 222222222222331')
      state.label = state.oldLabel ?? state.label

      initData()
    }
  )

  onMounted(() => {
    if (issueStatusList2.value.length == 0) {
      issueStatusList2.value = [
        {
          id: 37,
          name: '未开始',
        },
      ]
    }
    initData()
    if (
      redmine.third_user_id == productInfo?.value?.product_manager_uid ||
      !saveNode.value
    ) {
      state.isProductLeader = true
    }
  })

  defineExpose({
    save,
    cancelSubmit,
  })
</script>

<style lang="scss" scoped>
  :deep() {
    .el-form-item__content {
      line-height: 22px;
    }
    .el-form-item__label {
      justify-content: flex-end;
    }
  }
  .demo-form-inline .el-input {
    --el-input-width: 220px;
  }

  .demo-form-inline .el-select {
    --el-select-width: 220px;
  }
</style>
