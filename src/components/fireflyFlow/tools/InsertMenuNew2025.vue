<template>
  <div
    class="node-menu-wrapper"
    :style="{
      top: !isNaN(top) ? top + 'px' : top,
      left: !isNaN(left) ? left + 'px' : left,
    }"
  >
    <div style="margin: 0; text-align: right">
      <el-menu
        mode="vertical"
        class="node-menu"
        :open="true"
        @select="addCommonNode"
      >
        <el-menu-item
          v-for="(item, index) in state.commonNodeMap"
          :index="item.value"
          :key="index"
        >
          <span class="menu-item-inner">
            {{ item.label }}
          </span>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>

<script setup>
  import * as d3 from 'd3'
  import {
    flowNodePhase,
    flowNodeCustomLabel1,
    progressNodeCustomFieldPrecondition,
  } from '~/src/json/product'

  // 定义组件的 props
  const props = defineProps({
    lf: {
      type: Object,
      default: () => {},
    },
    show: {
      type: Boolean,
      default: false,
    },
    context: {
      type: Object,
      default: () => {},
    },
    graph: {
      type: Object,
      default: () => {},
    },
    position: {
      type: Object,
      default: () => {},
    },
    model: {
      type: Object,
      default: () => {},
    },
    showConnectBlock: {
      type: Boolean,
      default: false,
    },
    top: {
      type: Number,
      default: 0,
    },
    left: {
      type: Number,
      default: 0,
    },
  })

  // 定义事件
  const emit = defineEmits(['is-show'])

  // 定义常用节点映射
  const commonNodeMap = {
    one: {
      label: '主流程',
      value: 'one',
    },
    two: {
      label: '子流程',
      value: 'two',
    },
  }

  // 定义组件的内部状态
  const state = reactive({
    key: 1,
    preNodeId: '',
    commonNodeMap: { ...commonNodeMap },
    firstNodes: ['产品立项', '研发', '小批量', '大批量'],
  })

  // 点击菜单项时添加常见节点
  const addCommonNode = (v) => {
    let model = { ...props.model }
    let level
    switch (v) {
      case 'one':
        level = 1
        break
      case 'two':
        level =
          props.model.properties && props.model.properties.level
            ? props.model.properties.level + 1
            : 2
        break
    }
    handleAddNode(model, level)
    emit('is-show', false)
  }

  // 处理添加节点逻辑
  const handleAddNode = (entryNode, level = 1) => {
    console.log('entryNode:', entryNode)
    let newNodeValueY = entryNode.y
    let newNodeValueX = entryNode.x + 210 // 新节点的 x 坐标，确保在父节点右方

    const graphData = props.lf.getGraphData()
    const { edges, nodes } = graphData

    // 下一级的子节点
    const nextChildNode = edges
      .filter((edge) => edge.sourceAnchorId == entryNode.id + '_1')
      .map((edge) => nodes.find((item) => item.id == edge.targetNodeId))

    // 设置新节点的文本值
    const textValue = level > 1 ? '子节点' : '流程节点'

    // 创建新的节点数据
    const newNode = {
      type: 'task',
      x: newNodeValueX,
      y: newNodeValueY,
      text: {
        value: textValue,
        x: newNodeValueX,
        y: newNodeValueY,
      },
      properties: {
        level,
        status: 0,
        issue: {
          name: textValue,
          subject: textValue,
          tracker_id: 21, // 21为重要事项|流程节点
          status_id: 37, // 未开始
          issue_status: {
            name: '',
          },
        },
        label: {
          phase: {
            id: null,
            name: '',
            keywords: '',
            phase_id: 1,
            color: '#999999',
            options: flowNodePhase,
          },
          customLabels: [
            {
              id: null,
              name: '',
              keywords: '',
              color: '#999999',
              options: flowNodeCustomLabel1,
            },
          ],
          customLabelIds: [],
        },
        node_type: level > 1 ? 'task_node' : 'progress_node',
      },
    }

    if (props.containerId > 0) {
      newNode.properties.issue.project_id = props.containerId
    }
    if (entryNode.properties.issue_id) {
      newNode.properties.issue.parent_id = entryNode.properties.issue_id
    }

    // 添加新节点
    const addNode = props.lf.addNode(newNode)

    // 为新节点添加边
    props.lf.addEdge({
      sourceNodeId: entryNode.id,
      targetNodeId: addNode.id,
      properties: { level },
    })

    // 获取图数据并重新布局
    const graphData1 = props.lf.getGraphData()
    let layers = determineTreeLayers(graphData1.nodes, graphData1.edges)

    let progressNodeDepthMap = []

    // 提取得出所有流程节点
    let progressNodeLayers = []
    layers.forEach((layer) => {
      layer.forEach((node) => {
        if (node.properties.node_type == 'progress_node' || node.id == 1) {
          progressNodeLayers.push(node)
          progressNodeDepthMap[node.id] = node.properties.depth
        }
      })
    })

    // 找到当前新增节点的来源流程节点
    console.log('progressNodeLayers222222222222:', progressNodeLayers)
    console.log('layers1111111:', layers)
    let nodeIncomingNode = props.lf.getNodeIncomingNode(addNode.id)
    let sourceProgressNode = []
    let assistStack = [nodeIncomingNode[0].id]
    while (assistStack.length > 0) {
      let currentNodeId = assistStack.pop()
      let currentNode = nodes.find((node) => node.id == currentNodeId)
      if (
        currentNode.properties.node_type == 'progress_node' ||
        currentNode.id == 1
      ) {
        sourceProgressNode.push(currentNode)
        assistStack.length = 0
      } else {
        let incomingNodes = props.lf.getNodeIncomingNode(currentNodeId)
        assistStack.push(incomingNodes[0].id)
      }
    }

    // 非根流程节点，需要截取，只做部分布局
    if (sourceProgressNode[0].id != 1) {
      const splite = progressNodeDepthMap[sourceProgressNode[0].id]

      const filteredLayers = layers.filter((_, index) => index > splite)
      layers = []
      layers = filteredLayers
      layers.unshift(sourceProgressNode)
    }

    layers = getRelevantLayers(
      layers,
      graphData1,
      newNode,
      sourceProgressNode[0].properties.depth
    )

    console.log('layers:', layers)
    const layersWithVirtualNodes = addVirtualNodes(layers) // 添加虚拟节点占位
    // 计算右向树布局
    const nodePositions = calculateTreeLayout(
      layersWithVirtualNodes,
      graphData1,
      sourceProgressNode[0].id
    )
    console.log('nodePositions:', nodePositions)
    // 应用新的节点布局
    applyTreeLayout(nodePositions, props.lf)

    // 触发节点点击事件
    props.graph.eventCenter.emit('node:click', addNode)
  }

  const getRelevantLayers = (
    layers,
    graphData,
    entryNode,
    sourceProgressNodeLevel
  ) => {
    const { nodes, edges } = graphData
    const currentLevel = entryNode.properties.level // 当前新增节点的层级
    let relevantLayers = []
    let stopAtLevel = null
    relevantLayers.push(layers[0])

    console.log('currentLevel:', currentLevel)
    // 遍历所有层级，定位到当前新增节点所在的层级
    for (let level = 1; level < layers.length; level++) {
      const layer = layers[level]
      const taskNodes = layer.filter(
        (node) => node.properties.node_type === 'task_node'
      )

      // 如果是当前新增节点所在的层级，加入
      if (level === currentLevel) {
        relevantLayers.push(taskNodes)
      }

      // 加入往前或往后的层级，条件是node_type为task_node
      if (level < currentLevel) {
        relevantLayers.push(taskNodes)
      } else if (level > currentLevel) {
        relevantLayers.push(taskNodes)
      }
    }

    // 返回相关的层级节点
    return relevantLayers
  }

  // 为没有子节点的父节点添加虚拟子节点
  const addVirtualNodes = (layers, horizontalSpacing = 200) => {
    const newLayers = layers.map((layer) => [...layer])

    // 遍历每一层，找到没有子节点的父节点
    for (let i = 0; i < layers.length - 1; i++) {
      const currentLayer = layers[i]
      const nextLayer = layers[i + 1]

      const childrenMap = nextLayer.reduce((map, child) => {
        if (!map[child.parent_id]) {
          map[child.parent_id] = []
        }
        map[child.parent_id].push(child)
        return map
      }, {})

      currentLayer.forEach((node) => {
        const children = childrenMap[node.id] || []

        if (children.length === 0) {
          // 如果没有子节点，为该父节点添加一个虚拟子节点
          newLayers[i + 1].push({
            id: `${node.id}-virtual`, // 虚拟子节点的 ID
            parent_id: node.id, // 设置父节点 ID
            is_virtual: true, // 标记为虚拟节点
          })
        }
      })
    }

    return newLayers
  }

  // 确定树的层级
  // 确定树的层级并在每个节点的 properties 中增加 depth 字段
  const determineTreeLayers = (nodes, edges) => {
    const layers = []
    const nodeLayerMap = {}

    // 使用递归计算每个节点的层级，并将 depth 信息添加到 node.properties 中
    const dfs = (node, currentLayer) => {
      if (nodeLayerMap[node.id] !== undefined) return
      nodeLayerMap[node.id] = currentLayer

      // 添加 depth 到节点的 properties 中
      node.properties = node.properties || {} // 确保 properties 存在
      node.properties.depth = currentLayer // 标注节点的层级

      const parentNodeId = edges.filter(
        (edge) => edge.targetNodeId === node.id
      )?.[0]?.sourceNodeId
      node.parent_id = parentNodeId

      if (!layers[currentLayer]) layers[currentLayer] = []
      layers[currentLayer].push(node)

      const childEdges = edges.filter((edge) => edge.sourceNodeId === node.id)
      childEdges.forEach((edge) => {
        const targetNode = nodes.find((n) => n.id === edge.targetNodeId)
        dfs(targetNode, currentLayer + 1)
      })
    }

    // 从根节点开始递归
    nodes.forEach((node) => {
      const isRoot = !edges.some((edge) => edge.targetNodeId === node.id)
      if (isRoot) {
        dfs(node, 0)
      }
    })

    return layers
  }

  // 根据树结构计算从左到右布局
  const calculateTreeLayout = (
    layers,
    graphData1,
    rootNodeId = 1,
    horizontalSpacing = 160,
    verticalSpacing = 80
  ) => {
    const width = layers.length * horizontalSpacing
    const maxLayerLength = Math.max(...layers.map((layer) => layer.length))
    const height = maxLayerLength * verticalSpacing

    const treeData = transformToTreeData(layers, rootNodeId, graphData1)
    const root = d3.hierarchy(treeData)

    // 使用 D3 的树布局算法 给定一块画布大小
    const treeLayout = d3.tree().size([height, width])
    treeLayout(root)

    // 来源的流程节点
    const sourceProgressNode = graphData1.nodes.find((n) => n.id === rootNodeId)

    let beforeSourceProgressNodeY = sourceProgressNode.y
    let afterSourceProgressNodeY =
      root.descendants().find((item) => item.data.id == rootNodeId)?.x ?? 0
    let diff = beforeSourceProgressNodeY - afterSourceProgressNodeY // 节点增多画布增加，获取源流程节点的纵坐标偏移值

    // 获取原始坐标值并返回新的布局坐标
    const nodePositions = root.descendants().map((d, i) => {
      // 将原节点的坐标和新布局坐标合并
      return {
        id: d.data.id,
        x: d.y + sourceProgressNode.x, // 新布局中的横坐标
        y: d.x + diff, // 新布局中的纵坐标
      }
    })

    return nodePositions
  }

  // 递归转换节点为树状结构
  const transformToTreeData = (layers, rootId = 1, graphData1) => {
    const nodeMap = new Map()

    layers.forEach((layer) => {
      layer.forEach((node) => {
        nodeMap.set(node.id, { id: node.id, children: [] })
      })
    })

    layers.forEach((layer, layerIndex) => {
      if (layerIndex < layers.length - 1) {
        const nextLayer = layers[layerIndex + 1]
        layer.forEach((node) => {
          const children = nextLayer.filter(
            (child) => child.parent_id === node.id
          )
          const parentNode = nodeMap.get(node.id)
          children.forEach((child) => {
            const childNode = nodeMap.get(child.id)
            parentNode.children.push(childNode)
          })
        })
      }
    })

    const rootNode = graphData1.nodes.find((node) => node.id == rootId)
    return nodeMap.get(rootNode.id)
  }

  // 根据计算出的布局应用新位置
  const applyTreeLayout = (nodePositions, lf) => {
    nodePositions.forEach((pos) => {
      lf.graphModel.moveNode2Coordinate(pos.id, pos.x, pos.y, false)
    })
  }

  // 生命周期钩子：组件挂载后执行
  onMounted(() => {
    console.log('2025 onMounted')
    if (props.lf) {
      const graphData = props.lf.getGraphData()
      const { edges, nodes } = graphData
      let nodesMap = {}
      nodes.forEach((node) => {
        nodesMap[node.id] = node
      })
      let showSecondOnly = false
      if (
        (props.model.properties.level && props.model.properties.level > 1) ||
        (isTargetNode(props.model.id, edges) &&
          isSourceNode(props.model.id, edges)) ||
        (isSourceNode(props.model.id, edges) &&
          isTargetLevelFirst(props.model.id, edges, nodesMap))
      ) {
        showSecondOnly = true
      } else {
        showSecondOnly = false
      }
      console.log('showSecondOnly:', showSecondOnly)
      if (showSecondOnly) {
        delete state.commonNodeMap.one
        console.log('state.commonNodeMap:', state.commonNodeMap)
      }
    }
  })

  // 判断节点是否为目标节点
  const isTargetNode = (id, edges) => {
    return edges.some((edge) => edge.targetNodeId === id)
  }

  // 判断节点是否为源节点
  const isSourceNode = (id, edges) => {
    return edges.some((edge) => edge.sourceNodeId === id)
  }

  // 判断节点的目标是否为第一层
  const isTargetLevelFirst = (id, edges, nodeMap) => {
    return edges.some((edge) => {
      if (edge.sourceNodeId === id) {
        const targetNode = nodeMap[edge.targetNodeId]
        return (
          targetNode?.properties?.level && targetNode.properties.level === 1
        )
      }
    })
  }

  defineExpose({ handleAddNode })
</script>

<style lang="scss" scoped>
  .node-menu-wrapper {
    position: absolute;
    width: 100px;
    box-shadow: 0 2px 12px 0 rgba(31, 50, 82, 0.18);
  }
  .node-menu {
    width: 100%;
    height: 100%;
    max-height: 120px;
    padding: 4px;
    overflow-x: hidden;
    overflow-y: auto;
    background: #f3f6fa;
    border: none;
    border-radius: 4px;
    .el-menu-item {
      height: 25px;
      padding: 0 !important;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      font-weight: 400;
      line-height: 25px;
      color: #1f3252;
      text-align: left;
      &.is-active {
        background: rgba(41, 97, 239, 0.08);
        border-radius: 2px;
      }
    }
    .menu-item-inner {
      display: flex;
      align-items: center;
      margin-left: 2px;
    }
    .icon {
      width: 14px;
      height: 14px;
      margin-right: 4px;
      filter: drop-shadow(#2961ef 100px 0);
      transform: translateX(-100px);
    }
    &::-webkit-scrollbar {
      z-index: -1;
      width: 4px;
      /*滚动条整体样式*/
      background: transparent;
      border-radius: 5px;
    }

    &::-webkit-scrollbar-thumb {
      z-index: -1;
      background: #dcdfe6;
      /*滚动条里面小方块*/
      border-radius: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      /*滚动条里面轨道*/
      border-radius: 4px;
    }

    &::-webkit-scrollbar-corner {
      background: transparent;
      /*滚动条交汇处*/
      border-radius: 4px;
    }
  }

  .node-add-title {
    height: 24px;
    font-family: PingFangSC-Medium;
    font-size: 12px;
    font-weight: 500;
    line-height: 24px;
    color: #303a51;
  }
  .menu-item-inner {
    display: block;
    width: 100%;
    height: 100%;
  }
  .split-line {
    width: 100%;
    margin: 8px 0;
    border-bottom: 1px solid #dcdfe6;
  }
</style>
