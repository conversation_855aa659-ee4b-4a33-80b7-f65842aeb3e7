<template>
  <div
    class="node-menu-wrapper"
    :style="{
      top: !isNaN(top) ? top + 'px' : top,
      left: !isNaN(left) ? left + 'px' : left,
    }"
  >
    <div style="margin: 0; text-align: right">
      <el-menu
        mode="vertical"
        class="node-menu"
        :open="true"
        @select="addCommonNode"
      >
        <el-menu-item
          v-for="(item, index) in commonNodeMap"
          :index="item.value"
          :key="index"
        >
          <span class="menu-item-inner">
            {{ item.label }}
          </span>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>

<script>
  export default defineComponent({
    props: {
      lf: {
        type: Object,
        default: () => {},
      },
      show: {
        type: Boolean,
        default: false,
      },
      context: {
        type: Object,
        default: () => {},
      },
      graph: {
        type: Object,
        default: () => {},
      },
      position: {
        type: Object,
        default: () => {},
      },
      model: {
        type: Object,
        default: () => {},
      },
      showConnectBlock: {
        type: Boolean,
        default: false,
      },
      top: {
        type: Number,
        default: 0,
      },
      left: {
        type: Number,
        default: 0,
      },
    },
    emits: ['is-show'],
    setup(props, { emit }) {
      const commonNodeMap = {
        one: {
          label: '主流程',
          value: 'one',
        },
        two: {
          label: '子流程',
          value: 'two',
        },
      }
      const state = reactive({
        key: 1,
        preNodeId: '',
        commonNodeMap: Object.assign({}, commonNodeMap),
        firstNodes: ['产品立项', '研发', '小批量', '大批量'],
      })

      const addCommonNode = (v) => {
        let model = Object.assign({}, props.model)
        let level
        switch (v) {
          case 'one':
            level = 1
            break
          case 'two':
            if (props.model.properties && props.model.properties.level) {
              level = props.model.properties.level + 1
            } else {
              level = 2
            }
            break
        }
        // props.graph.eventCenter.emit('node:add-node', model)
        handleAddNode(model, level)
        emit('is-show', false)
      }

      const handleAddNode = (node, level = 1) => {
        let y = node.y
        let x = node.x + (node.width > 160 ? node.width + 20 : 160)
        const graphData = props.lf.getGraphData()
        const { edges } = graphData
        edges.forEach((edge) => {
          if (edge.sourceNodeId == node.id) {
            // 计算同级节点最大Y值
            if (edge.endPoint.y >= y) {
              const model = props.lf.getNodeModelById(edge.targetNodeId)
              y = edge.endPoint.y + 50
              x = model.x || edge.endPoint.x
            }
          }
        })

        let textValue = '流程节点'
        if (level > 1) {
          textValue = '子节点'
        }

        let newNode = {
          type: 'task',
          x: x,
          y: y,
          text: {
            value: textValue,
            x: x,
            y: y,
          },
          properties: {
            level: level,
            status: 0,
            issue: {
              name: textValue,
              subject: textValue,
              tracker_id: 21, //21为重要事项|流程节点
              status_id: 37, // 未开始
              issue_status: {
                name: '',
              },
            },
          },
        }
        if (props.containerId > 0) {
          newNode.properties.issue.project_id = props.containerId
        }
        if (node.properties.issue_id) {
          newNode.properties.issue.parent_id = node.properties.issue_id
        }

        props.lf.setDefaultEdgeType('tasklink')
        const addNode = props.lf.addNode(newNode)

        props.lf.addEdge({
          sourceNodeId: node.id,
          targetNodeId: addNode.id,
          properties: { level: level },
        })

        // node:click
        props.graph.eventCenter.emit('node:click', addNode)
      }

      const isTargetNode = (id, edges) => {
        for (let i = 0; i < edges.length; i++) {
          if (edges[i].targetNodeId == id) {
            return true
          }
        }
        return false
      }

      const isSourceNode = (id, edges) => {
        for (let i = 0; i < edges.length; i++) {
          if (edges[i].sourceNodeId == id) {
            return true
          }
        }
        return false
      }

      /**
       * 判断当前节点的目标节点是否存在一级节点
       * @param {*} id
       * @param {*} edges
       * @param {*} nodeMap
       */
      const isTargetLevelFirst = (id, edges, nodeMap) => {
        for (let i = 0; i < edges.length; i++) {
          if (edges[i].sourceNodeId == id) {
            if (
              typeof nodeMap[edges[i].targetNodeId] != 'undefined' &&
              nodeMap[edges[i].targetNodeId].properties &&
              nodeMap[edges[i].targetNodeId].properties.level &&
              nodeMap[edges[i].targetNodeId].properties.level == 1
            ) {
              return true
            }
          }
        }
        return false
      }

      onMounted(() => {
        if (props.lf) {
          const graphData = props.lf.getGraphData()
          const { edges, nodes } = graphData
          let nodesMap = {}
          nodes.forEach((node) => {
            nodesMap[node.id] = node
          })
          let showSecondOnly = false
          if (
            (props.model.properties.level &&
              props.model.properties.level > 1) ||
            (isTargetNode(props.model.id, edges) &&
              isSourceNode(props.model.id, edges)) ||
            (isSourceNode(props.model.id, edges) &&
              isTargetLevelFirst(props.model.id, edges, nodesMap))
            // (isSourceNode(props.model.id, edges) &&
            //   ((props.model.properties.level &&
            //     props.model.properties.level == 1) ||
            //     state.firstNodes.includes(props.model.text.value)))
          ) {
            showSecondOnly = true
          } else {
            showSecondOnly = false
          }
          console.log(showSecondOnly)
          if (showSecondOnly) {
            delete state.commonNodeMap.one
          }
        }
      })
      return {
        ...toRefs(state),
        addCommonNode,
      }
    },
  })
</script>

<style lang="scss" scoped>
  .node-menu-wrapper {
    position: absolute;
    width: 100px;
    box-shadow: 0 2px 12px 0 rgba(31, 50, 82, 0.18);
  }
  .node-menu {
    width: 100%;
    height: 100%;
    max-height: 120px;
    padding: 4px;
    overflow-x: hidden;
    overflow-y: auto;
    background: #f3f6fa;
    border: none;
    border-radius: 4px;
    .el-menu-item {
      height: 25px;
      padding: 0 !important;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      font-weight: 400;
      line-height: 25px;
      color: #1f3252;
      text-align: left;
      &.is-active {
        background: rgba(41, 97, 239, 0.08);
        border-radius: 2px;
      }
    }
    .menu-item-inner {
      display: flex;
      align-items: center;
      margin-left: 2px;
    }
    .icon {
      width: 14px;
      height: 14px;
      margin-right: 4px;
      filter: drop-shadow(#2961ef 100px 0);
      transform: translateX(-100px);
    }
    &::-webkit-scrollbar {
      z-index: -1;
      width: 4px;
      /*滚动条整体样式*/
      background: transparent;
      border-radius: 5px;
    }

    &::-webkit-scrollbar-thumb {
      z-index: -1;
      background: #dcdfe6;
      /*滚动条里面小方块*/
      border-radius: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      /*滚动条里面轨道*/
      border-radius: 4px;
    }

    &::-webkit-scrollbar-corner {
      background: transparent;
      /*滚动条交汇处*/
      border-radius: 4px;
    }
  }

  .node-add-title {
    height: 24px;
    font-family: PingFangSC-Medium;
    font-size: 12px;
    font-weight: 500;
    line-height: 24px;
    color: #303a51;
  }
  .menu-item-inner {
    display: block;
    width: 100%;
    height: 100%;
  }
  .split-line {
    width: 100%;
    margin: 8px 0;
    border-bottom: 1px solid #dcdfe6;
  }
</style>
