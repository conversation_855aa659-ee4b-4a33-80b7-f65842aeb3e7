import {
  flowNodePhase,
  flowNodeCustomLabel1,
  progressNodeCustomFieldPrecondition,
} from '~/src/json/product'

export const selectionConfig = {
  label: '选区',
  className: 'custom-selection',
  icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAApxJREFUWEftmM1u00AQx2eMJbjhOtwBKY7EifIG8ASUJ6Ac4xxo3sB5gqSH2kfCE8AjlCcgN6Q4UsOduO6tElYGzeaja9exvXI2oVL24jhez/zyn8zO7qDth9cAYMFixJHrHC0/a7mo+kPbD0kmiVwH5XvbD/sEcCx9FxMm3bj9airPs4JfL5DMvvRjAQFGket0M/YK/WVVKQcMJgMkWgPOEWKC5CwXEMyBQetoACGOonbzrC5gDABPl0ZuItdZhVtXiJX8pcKphaim0QNgTQE50f7vIQDtYDLgazbjdKGr+BOADT+85OvMdd7qgpLtqvg7AOZFRFnBoyD8zoau287JLkKs4k+EmOuo2Clk6qsuWBV/D2OZ0aXUNuweFKyr4sNIEpW0r6sIv6/i71BJtlJJVEqPaoiti/Fx3GmN5PeeBaHYlPxpO2KTUjS0b7cWp0aaIhjezG1+LQPKPte+Dtr+eAiAH9kxAVw+QuhVUW4Fqh2QQ2wg/pSVYVDC5FOV2o9sABAXx06im+z/RTUkefNtP+T/4Ot7zxCHc/jbKwItPbhvA7BxMT4lxC8bbMWAMJjfmudx9yWfmVOjFDDTSxEvz4neZJW+F0qCXtRxvJU32w/lA3seawxEXtRpncsPSwHZsWlgqtuQ3Jqj7K+1+leW+SRZt0gSSKZy6MRBiehzeUTSGY/84qr3ktdLKTdYbQZvUg0yr6rNvst47VksA3ENRoL3VSER4MdOARv+5ISAvlUA/I1E3qzTGu4UkMFsP+S+4vM8SKEYgicv5PsA5H4hNzrTywngh5nbFKfLVBZXkHurUzjbjccJt53Xg5Xb1NXYuYKLMN/VZ743EN5tqs97AZQX9SL1GH4vgMtkEfW5SL29AnJ9BsTTso7aP9GfT0LnGEEPAAAAAElFTkSuQmCC',
  callback: () => {
    lf.openSelectionSelect()
    lf.once('selection:selected', () => {
      lf.closeSelectionSelect()
    })
  },
}

export const resizableHtmlConfig = {
  type: 'ResizableHtml',
  label: '流程节点',
  // icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAEFVwZaAAAABGdBTUEAALGPC/xhBQAAAqlJREFUOBF9VM9rE0EUfrMJNUKLihGbpLGtaCOIR8VjQMGDePCgCCIiCNqzCAp2MyYUCXhUtF5E0D+g1t48qAd7CCLqQUQKEWkStcEfVGlLdp/fm3aW2QQdyLzf33zz5m2IsAZ9XhDpyaaIZkTS4ASzK41TFao88GuJ3hsr2pAbipHxuSYyKRugagICGANkfFnNh3HeE2N0b3nN2cgnpcictw5veJIzxmDamSlxxQZicq/mflxhbaH8BLRbuRwNtZp0JAhoplVRUdzmCe/vO27wFuuA3S5qXruGdboy5/PRGFsbFGKo/haRtQHIrM83bVeTrOgNhZReWaYGnE4aUQgTJNvijJFF4jQ8BxJE5xfKatZWmZcTQ+BVgh7s8SgPlCkcec4mGTmieTP4xd7PcpIEg1TX6gdeLW8rTVMVLVvb7ctXoH0Cydl2QOPJBG21STE5OsnbweVYzAnD3A7PVILuY0yiiyDwSm2g441r6rMSgp6iK42yqroI2QoXeJVeA+YeZSa47gZdXaZWQKTrG93rukk/l2Al6Kzh5AZEl7dDQy+JjgFahQjRopSxPbrbvK7GRe9ePWBo1wcU7sYrFZtavXALwGw/7Dnc50urrHJuTPSoO2IMV3gUQGNg87IbSOIY9BpiT9HV7FCZ94nPXb3MSnwHn/FFFE1vG6DTby+r31KAkUktB3Qf6ikUPWxW1BkXSPQeMHHiW0+HAd2GelJsZz1OJegCxqzl+CLVHa/IibuHeJ1HAKzhuDR+ymNaRFM+4jU6UWKXorRmbyqkq/D76FffevwdCp+jN3UAN/C9JRVTDuOxC/oh+EdMnqIOrlYteKSfadVRGLJFJPSB/ti/6K8f0CNymg/iH2gO/f0DwE0yjAFO6l8JaR5j0VPwPwfaYHqOqrCI319WzwhwzNW/aQAAAABJRU5ErkJggg==',
  icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAAH6ji2bAAAABGdBTUEAALGPC/xhBQAAAnBJREFUOBGdVL1rU1EcPfdGBddmaZLiEhdx1MHZQXApraCzQ7GKLgoRBxMfcRELuihWKcXFRcEWF8HBf0DdDCKYRZpnl7p0svLe9Zzbd29eQhTbC8nv+9zf130AT63jvooOGS8Vf9Nt5zxba7sXQwODfkWpkbjTQfCGUd9gIp3uuPP8bZ946g56dYQvnBg+b1HB8VIQmMFrazKcKSvFW2dQTxJnJdQ77urmXWOMBCmXM2Rke4S7UAW+/8ywwFoewmBps2tu7mbTdp8VMOkIRAkKfrVawalJTtIliclFbaOBqa0M2xImHeVIfd/nKAfVq/LGnPss5Kh00VEdSzfwnBXPUpmykNss4lUI9C1ga+8PNrBD5YeqRY2Zz8PhjooIbfJXjowvQJBqkmEkVnktWhwu2SM7SMx7Cj0N9IC0oQXRo8xwAGzQms+xrB/nNSUWVveI48ayrFGyC2+E2C+aWrZHXvOuz+CiV6iycWe1Rd1Q6+QUG07nb5SbPrL4426d+9E1axKjY3AoRrlEeSQo2Eu0T6BWAAr6COhTcWjRaYfKG5csnvytvUr/WY4rrPMB53Uo7jZRjXaG6/CFfNMaXEu75nG47X+oepU7PKJvvzGDY1YLSKHJrK7vFUwXKkaxwhCW3u+sDFMVrIju54RYYbFKpALZAo7sB6wcKyyrd+aBMryMT2gPyD6GsQoRFkGHr14TthZni9ck0z+Pnmee460mHXbRAypKNy3nuMdrWgVKj8YVV8E7PSzp1BZ9SJnJAsXdryw/h5ctboUVi4AFiCd+lQaYMw5z3LGTBKjLQOeUF35k89f58Vv/tGh+l+PE/wG0rgfIUbZK5AAAAABJRU5ErkJggg==',
  // icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIGZpbGw9IiMzOTc3RjMiIHN0cm9rZT0iI0RDREZFNiIvPgo8cmVjdCB4PSI0LjUiIHk9IjcuMjk5OCIgd2lkdGg9IjciIGhlaWdodD0iMS40IiByeD0iMC43IiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB4PSI4LjcwMDIiIHk9IjQuNSIgd2lkdGg9IjciIGhlaWdodD0iMS40IiByeD0iMC43IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA4LjcwMDIgNC41KSIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==',
  className: 'important-node',
  properties: {
    height: 42,
    fields: [
      {
        key: 'id',
        type: 'string',
      },
      {
        key: 'name',
        type: 'string',
      },
      {
        key: 'age',
        type: 'integer',
      },
    ],
    issue: {
      issue_status: {
        name: '',
      },
      name: '流程节点',
      status_id: 37,
      subject: '流程节点',
      description: '',
      tracker_id: 21,
      start_date: '',
      due_date: '',
      custom_fields: [progressNodeCustomFieldPrecondition],
    },
    label: {
      phase: {
        id: null,
        name: '',
        keywords: '',
        phase_id: 1,
        color: '#999999',
        options: flowNodePhase,
      },
      customLabels: [
        {
          id: null,
          name: '',
          keywords: '',
          color: '#999999',
          options: flowNodeCustomLabel1,
        },
      ],
      customLabelIds: [],
    },
    level: 1,
    node_type: 'progress_node',
    status: 0,
  },
}

export const resizableHtmlChildConfig = {
  type: 'ResizableHtml',
  label: '子任务节点',
  // icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAEFVwZaAAAABGdBTUEAALGPC/xhBQAAAqlJREFUOBF9VM9rE0EUfrMJNUKLihGbpLGtaCOIR8VjQMGDePCgCCIiCNqzCAp2MyYUCXhUtF5E0D+g1t48qAd7CCLqQUQKEWkStcEfVGlLdp/fm3aW2QQdyLzf33zz5m2IsAZ9XhDpyaaIZkTS4ASzK41TFao88GuJ3hsr2pAbipHxuSYyKRugagICGANkfFnNh3HeE2N0b3nN2cgnpcictw5veJIzxmDamSlxxQZicq/mflxhbaH8BLRbuRwNtZp0JAhoplVRUdzmCe/vO27wFuuA3S5qXruGdboy5/PRGFsbFGKo/haRtQHIrM83bVeTrOgNhZReWaYGnE4aUQgTJNvijJFF4jQ8BxJE5xfKatZWmZcTQ+BVgh7s8SgPlCkcec4mGTmieTP4xd7PcpIEg1TX6gdeLW8rTVMVLVvb7ctXoH0Cydl2QOPJBG21STE5OsnbweVYzAnD3A7PVILuY0yiiyDwSm2g441r6rMSgp6iK42yqroI2QoXeJVeA+YeZSa47gZdXaZWQKTrG93rukk/l2Al6Kzh5AZEl7dDQy+JjgFahQjRopSxPbrbvK7GRe9ePWBo1wcU7sYrFZtavXALwGw/7Dnc50urrHJuTPSoO2IMV3gUQGNg87IbSOIY9BpiT9HV7FCZ94nPXb3MSnwHn/FFFE1vG6DTby+r31KAkUktB3Qf6ikUPWxW1BkXSPQeMHHiW0+HAd2GelJsZz1OJegCxqzl+CLVHa/IibuHeJ1HAKzhuDR+ymNaRFM+4jU6UWKXorRmbyqkq/D76FffevwdCp+jN3UAN/C9JRVTDuOxC/oh+EdMnqIOrlYteKSfadVRGLJFJPSB/ti/6K8f0CNymg/iH2gO/f0DwE0yjAFO6l8JaR5j0VPwPwfaYHqOqrCI319WzwhwzNW/aQAAAABJRU5ErkJggg==',
  // icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAAH6ji2bAAAABGdBTUEAALGPC/xhBQAAA1BJREFUOBFtVE1IVUEYPXOf+tq40Y3vPcmFIdSjIorWoRG0ERWUgnb5FwVhYQSl72oUoZAboxKNFtWiwKRN0M+jpfSzqJAQclHo001tKkjl3emc8V69igP3znzfnO/M9zcDcKT67azmjYWTwl9Vn7Vumeqzj1DVb6cleQY4oAVnIOPb+mKAGxQmKI5CWNJ2aLPatxWa3aB9K7/fB+/Z0jUF6TmMlFLQqrkECWQzOZxYGjTlOl8eeKaIY5yHnFn486xBustDjWT6dG7pmjHOJd+33t0iitTPkK6tEvjxq4h2MozQ6WFSX/LkDUGfFwfhEZj1Auz/U4pyAi5Sznd7uKzznXeVHlI/Aywmk6j7fsUsEuCGADrWARXXwjxWQsUbIupDHJI7kF5dRktg0eN81IbiZXiTESic50iwS+t1oJgL83jAiBupLDCQqwziaWSoAFSeIR3P5Xv5az00wyIn35QRYTwdSYbz8pH8fxUUAtxnFvYmEmgI0wYXUXcCCSpeEVpXlsRhBnCEATxWylL9+EKCAYhe1NGstUa6356kS9NVvt3DU2fd+Wtbm/+lSbylJqsqkSm9CRhvoJVlvKPvF1RKY/FcPn5j4UfIMLn8D4UYb54BNsilTDXKnF4CfTobA0FpoW/LSp306wkXM+XaOJhZaFkcNM82ASNAWMrhrUbRfmyeI1FvRBTpN06WKxa9BK0o2E4Pd3zfBBEwPsv9sQBnmLVbLEIZ/Xe9LYwJu/Er17W6HYVBc7vmuk0xUQ+pqxdom5Fnp55SiytXLPYoMXNM4u4SNSCFWnrVIzKG3EGyMXo6n/BQOe+bX3FClY4PwydVhthOZ9NnS+ntiLh0fxtlUJHAuGaFoVmttpVMeum0p3WEXbcll94l1wM/gZ0Ccczop77VvN2I7TlsZCsuXf1WHvWEhjO8DPtyOVg2/mvK9QqboEth+7pD6NUQC1HN/TwvydGBARi9MZSzLE4b8Ru3XhX2PBxf8E1er2A6516o0w4sIA+lwURhAON82Kwe2iDAC1Watq4XHaGQ7skLcFOtI5lDxuM2gZe6WFIotPAhbaeYlU4to5cuarF1QrcZ/lwrLaCJl66JBocYZnrNlvm2+MBCTmUymPrYZVbjdlr/BxlMjmNmNI3SAAAAAElFTkSuQmCC',
  icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIGZpbGw9IiMzOTc3RjMiIHN0cm9rZT0iI0RDREZFNiIvPgo8cmVjdCB4PSI0LjUiIHk9IjcuMjk5OCIgd2lkdGg9IjciIGhlaWdodD0iMS40IiByeD0iMC43IiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB4PSI4LjcwMDIiIHk9IjQuNSIgd2lkdGg9IjciIGhlaWdodD0iMS40IiByeD0iMC43IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA4LjcwMDIgNC41KSIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==',

  className: 'important-node',
  properties: {
    height: 42,
    fields: [
      {
        key: 'id',
        type: 'string',
      },
      {
        key: 'name',
        type: 'string',
      },
      {
        key: 'age',
        type: 'integer',
      },
    ],
    issue: {
      issue_status: {
        name: '',
      },
      name: '子节点',
      status_id: 37,
      subject: '子节点',
      description: '',
      tracker_id: 21,
      start_date: '',
      due_date: '',
      custom_fields: [progressNodeCustomFieldPrecondition],
    },
    label: {
      phase: {
        id: 1,
        name: '',
        keywords: '',
        phase_id: 1,
        color: '#999999',
        options: flowNodePhase,
      },
      customLabels: [
        {
          id: 1,
          name: '',
          keywords: '',
          phase_id: 1,
          color: '#999999',
          options: flowNodeCustomLabel1,
        },
        // {
        //   id: 1,
        //   name: '',
        //   keywords: '',
        //   phase_id: 1,
        //   color: '#999999',
        //   options: flowNodeCustomLabel1,
        // }
      ],
      customLabelIds: [],
    },
    level: 1,
    node_type: 'task_node',
    status: 0,
  },
}

export const taskConfig = {
  type: 'task',
  label: '流程节点',
  properties: {
    height: 32,
    issue: {
      issue_status: {
        name: '',
      },
      name: '流程节点',
      status_id: 37,
      subject: '流程节点',
      tracker_id: 21,
    },
    level: 1,
    node_type: 'progress_node',
    status: 0,
    width: 106,
  },
  text: {
    value: '流程节点',
  },
}

// 左侧面板
export const setDndPanel = (lf) => {
  lf.extension.dndPanel.setPatternItems([
    selectionConfig,
    resizableHtmlConfig,
    // resizableHtmlChildConfig,
    // taskConfig,
  ])
}
