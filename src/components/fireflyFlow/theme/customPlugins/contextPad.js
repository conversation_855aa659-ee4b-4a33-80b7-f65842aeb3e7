import { createApp } from 'vue'
import InsertMenuNew2025 from '~/src/components/fireflyFlow/tools/InsertMenuNew2025.vue'
import * as d3 from 'd3'
import {
  flowNodePhase,
  flowNodeCustomLabel1,
  progressNodeCustomFieldPrecondition,
} from '~/src/json/product'

// const app = createApp(InsertMenuNew2025)
// const vm = app.mount('#app')

// import { handleAddNode } from '~/src/components/fireflyFlow/tools/InsertMenuNew2025.vue'

const COMMON_TYPE_KEY = 'menu-common'
const NEXT_X_DISTANCE = 200
const NEXT_Y_DISTANCE = 100
let vm = null

class ContextPad {
  constructor({ lf }) {
    this.menuTypeMap = new Map()
    this.lf = lf
    this.__menuDOM = document.createElement('div')
    this.__menuDOM.className = 'lf-inner-context'
    this.menuTypeMap.set(COMMON_TYPE_KEY, [])
  }
  render(lf, container) {
    this.container = container
    lf.on('node:click', ({ data }) => {
      this._activeData = data
      this._activeData && this.createContextMenu()
    })
    lf.on('edge:click', ({ data }) => {
      // 获取右上角坐标
      this._activeData = data
      this._activeData && this.createContextMenu()
    })
    lf.on('blank:click', () => {
      this._activeData && this.hideContextMenu()
    })
  }
  setContextMenuByType(type, menus) {
    this.menuTypeMap.set(type, menus)
  }
  /**
   * 隐藏菜单
   */
  hideContextMenu() {
    this.__menuDOM.innerHTML = ''
    this.__menuDOM.style.display = 'none'
    if (this.isShow) {
      this.container.removeChild(this.__menuDOM)
    }
    this.lf.off(
      'node:delete,edge:delete,node:drag,graph:transform',
      this.listenDelete
    )
    this.isShow = false
  }
  /**
   * 显示指定元素菜单
   * @param data 节点id、节点类型、菜单位置
   */
  showContextMenu(data) {
    if (!data || !data.id) {
      console.warn('请检查传入的参数')
      return
    }
    this._activeData = data
    this.createContextMenu()
  }
  setContextMenuItems(menus) {
    this.menuTypeMap.set(COMMON_TYPE_KEY, menus)
  }
  /**
   * 获取新菜单位置
   */
  getContextMenuPosition() {
    const data = this._activeData
    const Model = this.lf.graphModel.getElement(data.id)
    if (!Model || this._activeData.type != 'ResizableHtml') {
      console.warn(`找不到元素${data.id}`)
      return
    }
    let x
    let y
    if (Model.BaseType === 'edge') {
      x = Number.MIN_SAFE_INTEGER
      y = Number.MAX_SAFE_INTEGER
      const edgeData = Model.getData()
      x = Math.max(edgeData.startPoint.x, x)
      y = Math.min(edgeData.startPoint.y, y)
      x = Math.max(edgeData.endPoint.x, x)
      y = Math.min(edgeData.endPoint.y, y)
      if (edgeData.pointsList) {
        edgeData.pointsList.forEach((point) => {
          x = Math.max(point.x, x)
          y = Math.min(point.y, y)
        })
      }
    }
    if (Model.BaseType === 'node') {
      x = data.x + Model.width / 2
      y = data.y - Model.height / 2
    }
    return this.lf.graphModel.transformModel.CanvasPointToHtmlPoint([x, y])
  }
  createContextMenu() {
    const { isSilentMode } = this.lf.options
    // 静默模式不显示菜单
    if (isSilentMode) {
      return
    }
    let items = this.menuTypeMap.get(this._activeData.type) || []
    items = items.concat(this.menuTypeMap.get(COMMON_TYPE_KEY))
    const menus = document.createDocumentFragment()
    items.forEach((item) => {
      const menuItem = document.createElement('div')
      menuItem.className = 'lf-context-item'
      const img = document.createElement('img')
      img.src = item.icon
      img.className = 'lf-context-img'
      if (item.className) {
        menuItem.className = `${menuItem.className} ${item.className}`
      }
      img.addEventListener('click', () => {
        this.hideContextMenu()
        if (item.callback) {
          item.callback(this._activeData)
        } else {
          const node = this.lf.graphModel.getElement(this._activeData.id)
          console.log('node:', node)
          this.handleAddNode(node)
          // this.vm.handleAddNode(node) // 调用暴露的函数
          // this.addNode({
          //   sourceId: this._activeData.id,
          //   x: this._activeData.x,
          //   y: this._activeData.y,
          //   properties: item.properties,
          //   type: item.type,
          // })
        }
      })
      menuItem.appendChild(img)
      menus.appendChild(menuItem)
    })
    this.__menuDOM.innerHTML = ''
    this.__menuDOM.appendChild(menus)
    this._activeData &&
      this._activeData.type == 'ResizableHtml' &&
      this.showMenu()
  }

  addNode(node, y) {
    const isDeep = y !== undefined
    if (y === undefined) {
      y = node.y
    }
    const nodeModel = this.lf.getNodeModelById(node.sourceId)
    const leftTopX = node.x - 100 + NEXT_X_DISTANCE
    const leftTopY = y - node.y / 2 - 20
    const rightBottomX = node.x + 100 + NEXT_X_DISTANCE
    const rightBottomY = y + node.y / 2 + 20
    const existElements = this.lf.getAreaElement(
      [leftTopX, leftTopY],
      [rightBottomX, rightBottomY]
    )
    console.log(existElements)
    if (existElements.length) {
      y = y + NEXT_Y_DISTANCE
      this.addNode(node, y)
      return
    }
    const newNode = this.lf.addNode({
      type: node.type,
      x: node.x + 200,
      y,
      properties: node.properties,
    })
    let startPoint
    let endPoint
    if (isDeep) {
      startPoint = {
        x: node.x,
        y: node.y + nodeModel.height / 2,
      }
      endPoint = {
        x: newNode.x - newNode.width / 2,
        y: newNode.y,
      }
    }
    this.lf.addEdge({
      sourceNodeId: node.sourceId,
      targetNodeId: newNode.id,
      startPoint,
      endPoint,
    })
  }

  showMenu() {
    const [x, y] = this.getContextMenuPosition()
    this.__menuDOM.style.display = 'flex'
    this.__menuDOM.style.top = `${y}px`
    this.__menuDOM.style.left = `${x + 10}px`
    this.container.appendChild(this.__menuDOM)
    // 菜单显示的时候，监听删除，同时隐藏
    !this.isShow &&
      this.lf.on(
        'node:delete,edge:delete,node:drag,graph:transform',
        this.listenDelete
      )
    this.isShow = true
  }

  listenDelete() {
    this.hideContextMenu()
  }

  ////////////////////////////////////////
  ///////////////////////////
  /////////////
  // 布局逻辑部分
  handleAddNode(entryNode) {
    let level =
      entryNode.properties && entryNode.properties.level
        ? entryNode.properties.level + 1
        : 2

    let newNodeValueY = entryNode.y
    let newNodeValueX = entryNode.x + 210 // 新节点的 x 坐标，确保在父节点右方

    const graphData = this.lf.getGraphData()
    const { edges, nodes } = graphData

    // 设置新节点的文本值
    const textValue = '子节点'

    // 创建新的节点数据
    const newNode = {
      type: 'ResizableHtml',
      x: newNodeValueX,
      y: newNodeValueY,
      // text: {
      //   value: textValue,
      //   x: newNodeValueX,
      //   y: newNodeValueY,
      // },
      properties: {
        height: 42,
        level,
        status: 0,
        issue: {
          name: textValue,
          subject: textValue,
          description: '',
          tracker_id: 21, // 21为重要事项|流程节点
          status_id: 37, // 未开始
          start_date: '',
          due_date: '',
          issue_status: {
            name: '',
          },
          custom_fields: [progressNodeCustomFieldPrecondition],
        },
        label: {
          phase: {
            id: null,
            name: '',
            keywords: '',
            phase_id: 1,
            color: '#999999',
            options: flowNodePhase,
          },
          customLabels: [
            {
              id: null,
              name: '',
              keywords: '',
              color: '#999999',
              options: flowNodeCustomLabel1,
            },
          ],
          customLabelIds: [],
        },
        node_type: 'task_node',
      },
    }

    if (entryNode.properties.issue_id) {
      newNode.properties.issue.parent_id = entryNode.properties.issue_id
    }

    // // 添加新节点
    // const addNode = this.lf.addNode(newNode)

    // // 为新节点添加左侧边
    // this.lf.addEdge({
    //   sourceNodeId: entryNode.id,
    //   targetNodeId: addNode.id,
    //   properties: { level },
    // })
    const addNode = this.addOrInsertNode(this.lf, entryNode, newNode, level)

    let edgeOfNewNode = this.lf.getEdgeModels({ sourceNodeId: addNode.id })

    // 获取图数据并重新布局
    const graphData1 = this.lf.getGraphData()

    let layers = this.determineTreeLayers(graphData1.nodes, graphData1.edges)

    let progressNodeDepthMap = []
    let progressNextNodeMap = {} // 新建一个 map 用来保存 progress_node 之间的顺序关系
    let previousNode = null // 用来记录上一个 progress_node

    // 提取得出所有流程节点
    let progressNodeLayers = []
    layers.forEach((layer) => {
      layer.forEach((node) => {
        if (node.properties.node_type == 'progress_node' || node.id == 1) {
          progressNodeLayers.push(node)
          progressNodeDepthMap[node.id] = node.properties.depth

          // 如果 previousNode 存在，表示这是当前节点之后的节点，更新顺序关系
          if (previousNode) {
            progressNextNodeMap[previousNode.id] = node.id
          }

          // 更新 previousNode 为当前节点
          previousNode = node
        }
      })
    })

    // 新增节点
    // const addNode = this.addOrInsertNode(lf, entryNode, newNode, level)

    // 找到当前新增节点的来源流程节点
    console.log('layers1111111:', layers)
    let nodeIncomingNode = this.lf.getNodeIncomingNode(addNode.id)
    let sourceProgressNode = []
    let assistStack = [nodeIncomingNode[0].id]

    while (assistStack.length > 0) {
      let currentNodeId = assistStack.pop()
      let currentNode = nodes.find((node) => node.id == currentNodeId)
      if (
        currentNode.properties.node_type == 'progress_node' ||
        currentNode.id == 1
      ) {
        sourceProgressNode.push(currentNode)
        assistStack.length = 0
      } else {
        let incomingNodes = this.lf.getNodeIncomingNode(currentNodeId)
        assistStack.push(incomingNodes[0].id)
      }
    }

    // 非根流程节点，需要截取，只做部分布局
    // if (sourceProgressNode[0].id != 1) {
    const splite = progressNodeDepthMap[sourceProgressNode[0].id]

    // 查找下一个符合条件的节点（node_type == 'progress_node'）在 sourceProgressNode 之后
    let nextProgressNode = null
    let foundSourceNode = false // 用来标识是否找到了 sourceProgressNode

    for (let i = 0; i < layers.length; i++) {
      for (let j = 0; j < layers[i].length; j++) {
        let node = layers[i][j]

        // 找到 sourceProgressNode 后开始查找下一个符合条件的节点
        if (foundSourceNode && node.properties.node_type == 'progress_node') {
          nextProgressNode = node
          break
        }

        // 标记找到 sourceProgressNode
        if (node.id === sourceProgressNode[0].id) {
          foundSourceNode = true
        }
      }

      // 如果找到下一个符合条件的节点，退出循环
      if (nextProgressNode) {
        break
      }
    }

    // 获取下一个符合条件节点的 splite
    let nextSplite = null
    if (nextProgressNode) {
      nextSplite = progressNodeDepthMap[nextProgressNode.id]
    }

    // 处理截取部分，取出位于 splite 和 nextSplite 之间的 layers 内容
    let filteredLayers = []
    if (nextSplite !== null) {
      filteredLayers = layers.filter(
        (_, index) => index > splite && index < nextSplite
      )
    } else {
      // 如果没有找到下一个符合条件的节点，则截取到最后
      filteredLayers = layers.filter((_, index) => index > splite)
    }

    // const filteredLayers = layers.filter((_, index) => index > splite)
    layers = []
    layers = filteredLayers
    layers.unshift(sourceProgressNode)

    console.log('layers22222222222222:', layers)

    layers = this.getRelevantLayers(
      layers,
      graphData1,
      newNode,
      sourceProgressNode[0].properties.depth
    )

    // 新节点的同胞节点（包括其本身）
    let nextNodesOfEntryNode = this.lf.getNodeOutgoingNode(entryNode.id)

    // 新节点的x值或同其同胞节点的x值
    let newAddNodeSetX =
      nextNodesOfEntryNode.length == 1 &&
      nextNodesOfEntryNode[0].id == addNode.id
        ? null
        : nextNodesOfEntryNode[0]?.x

    const layersWithVirtualNodes = this.addVirtualNodes(layers) // 添加虚拟节点占位
    // 计算右向树布局
    const nodePositions = this.calculateTreeLayout(
      layersWithVirtualNodes,
      graphData1,
      sourceProgressNode[0].id, // 顶头上的流程类型节点id
      newAddNodeSetX, // 新增节点的x值
      progressNodeDepthMap, // 流程类型节点Map
      nextNodesOfEntryNode // 新增节点及其同胞节点
    )
    // 应用新的节点布局
    this.applyTreeLayout(nodePositions, this.lf)

    // 触发节点点击事件
    // this.lf.graphModel.eventCenter.emit('node:click', addNode)
  }

  // 确定树的层级
  // 确定树的层级并在每个节点的 properties 中增加 depth 字段
  determineTreeLayers(nodes, edges) {
    const layers = []
    const nodeLayerMap = {}

    // 使用递归计算每个节点的层级，并将 depth 信息添加到 node.properties 中
    const dfs = (node, currentLayer) => {
      if (nodeLayerMap[node.id] !== undefined) return
      nodeLayerMap[node.id] = currentLayer

      // 添加 depth 到节点的 properties 中
      node.properties = node.properties || {} // 确保 properties 存在
      node.properties.depth = currentLayer // 标注节点的层级

      const parentNodeId = edges.filter(
        (edge) => edge.targetNodeId === node.id
      )?.[0]?.sourceNodeId
      node.parent_id = parentNodeId

      if (!layers[currentLayer]) layers[currentLayer] = []
      layers[currentLayer].push(node)

      const childEdges = edges.filter((edge) => edge.sourceNodeId === node.id)
      childEdges.forEach((edge) => {
        const targetNode = nodes.find((n) => n.id === edge.targetNodeId)
        dfs(targetNode, currentLayer + 1)
      })
    }

    // 从根节点开始递归
    nodes.forEach((node) => {
      const isRoot = !edges.some((edge) => edge.targetNodeId === node.id)
      if (isRoot) {
        dfs(node, 0)
      }
    })

    return layers
  }

  getRelevantLayers(layers, graphData, entryNode, sourceProgressNodeLevel) {
    const { nodes, edges } = graphData
    const currentLevel = entryNode.properties.level // 当前新增节点的层级
    let relevantLayers = []
    let stopAtLevel = null
    relevantLayers.push(layers[0])

    // 遍历所有层级，定位到当前新增节点所在的层级
    for (let level = 1; level < layers.length; level++) {
      const layer = layers[level]
      const taskNodes = layer.filter(
        (node) => node.properties.node_type === 'task_node'
      )

      // 如果是当前新增节点所在的层级，加入
      if (level === currentLevel) {
        relevantLayers.push(taskNodes)
      }

      // 加入往前或往后的层级，条件是node_type为task_node
      if (level < currentLevel) {
        relevantLayers.push(taskNodes)
      } else if (level > currentLevel) {
        relevantLayers.push(taskNodes)
      }
    }

    // 返回相关的层级节点
    return relevantLayers
  }

  // 为没有子节点的父节点添加虚拟子节点
  addVirtualNodes(layers, horizontalSpacing = 200) {
    const newLayers = layers.map((layer) => [...layer])

    // 遍历每一层，找到没有子节点的父节点
    for (let i = 0; i < layers.length - 1; i++) {
      const currentLayer = layers[i]
      const nextLayer = layers[i + 1]

      const childrenMap = nextLayer.reduce((map, child) => {
        if (!map[child.parent_id]) {
          map[child.parent_id] = []
        }
        map[child.parent_id].push(child)
        return map
      }, {})

      currentLayer.forEach((node) => {
        const children = childrenMap[node.id] || []

        if (children.length === 0) {
          // 如果没有子节点，为该父节点添加一个虚拟子节点
          newLayers[i + 1].push({
            id: `${node.id}-virtual`, // 虚拟子节点的 ID
            parent_id: node.id, // 设置父节点 ID
            is_virtual: true, // 标记为虚拟节点
          })
        }
      })
    }

    return newLayers
  }

  // 根据树结构计算从左到右布局
  calculateTreeLayout(
    layers,
    graphData1,
    rootNodeId = 1,
    sameLevelNodeXDistance = null,
    progressNodeDepthMap,
    nextNodesOfEntryNode,
    horizontalSpacing = 180,
    verticalSpacing = 80
  ) {
    console.log('layers.length3333333333333333:', layers.length)
    // 动态计算系数，随着 layers.length 增加逐步增大
    const scaleFactor = 1 + (layers.length - 1) * 0.1 // 每增加一层，scaleFactor 增加 0.1
    const width = layers.length * scaleFactor * horizontalSpacing
    const maxLayerLength = Math.max(...layers.map((layer) => layer.length))
    const height = maxLayerLength * verticalSpacing

    const treeData = this.transformToTreeData(layers, rootNodeId, graphData1)
    const root = d3.hierarchy(treeData)

    // 给定一块画布大小 使用 D3 的树布局算法
    const treeLayout = d3.tree().size([height, width])
    treeLayout(root)

    // 来源的流程节点
    const sourceProgressNode = graphData1.nodes.find((n) => n.id === rootNodeId)

    let beforeSourceProgressNodeY = sourceProgressNode.y
    let afterSourceProgressNodeY =
      root.descendants().find((item) => item.data.id == rootNodeId)?.x ?? 0
    let diff = beforeSourceProgressNodeY - afterSourceProgressNodeY // 节点增多画布增加，获取源流程节点的纵坐标偏移值

    // 获取原始坐标值并返回新的布局坐标
    const nodePositions = root.descendants().map((d, i) => {
      let xValue = sameLevelNodeXDistance ?? d.y + sourceProgressNode.x
      if (
        progressNodeDepthMap[d.data.id] !== undefined ||
        !nextNodesOfEntryNode.find((item) => item.id == d.data.id)
      ) {
        xValue = d.y + sourceProgressNode.x
      }

      // 将原节点的坐标和新布局坐标合并
      return {
        id: d.data.id,
        x: xValue, // 新布局中的横坐标
        y: d.x + diff, // 新布局中的纵坐标
      }
    })

    return nodePositions
  }

  // 递归转换节点为树状结构
  transformToTreeData(layers, rootId = 1, graphData1) {
    const nodeMap = new Map()

    layers.forEach((layer) => {
      layer.forEach((node) => {
        nodeMap.set(node.id, { id: node.id, children: [] })
      })
    })

    layers.forEach((layer, layerIndex) => {
      if (layerIndex < layers.length - 1) {
        const nextLayer = layers[layerIndex + 1]
        layer.forEach((node) => {
          const children = nextLayer.filter(
            (child) => child.parent_id === node.id
          )
          const parentNode = nodeMap.get(node.id)
          children.forEach((child) => {
            const childNode = nodeMap.get(child.id)
            parentNode.children.push(childNode)
          })
        })
      }
    })

    const rootNode = graphData1.nodes.find((node) => node.id == rootId)
    return nodeMap.get(rootNode.id)
  }

  // 根据计算出的布局应用新位置
  applyTreeLayout(nodePositions, lf) {
    nodePositions.forEach((pos) => {
      lf.graphModel.moveNode2Coordinate(pos.id, pos.x, pos.y, false)
    })
  }

  /**
   * 根据 entryNode 及其出边，决定是直接添加新节点，还是在两个节点之间插入新节点
   * @param {Object} lf LogicFlow 实例
   * @param {Object} entryNode 当前的入口节点
   * @param {Object} newNode 新的节点数据
   * @param {number} level 节点的层级
   * @returns {Object} 添加的新节点对象
   */
  addOrInsertNode(lf, entryNode, newNode, level) {
    let addNode = null

    // 获取所有以 entryNode 为起点的边
    const outgoingEdges = lf.getEdgeModels({ sourceNodeId: entryNode.id })

    // 获取所有以 entryNode 为起点的节点
    const outgoingNodes = outgoingEdges.map((edge) =>
      lf.getNodeDataById(edge.targetNodeId)
    )

    // 循环访问 outgoingNodes 中的每个节点，并获取它们的目标节点
    let nextProgressNodes = []
    _.forEach(outgoingNodes, (node) => {
      if (node.properties.node_type !== 'progress_node') {
        const outgoingNodeEdges = lf.getEdgeModels({
          sourceNodeId: node.id,
        })
        // 获取每个节点的目标节点
        const nextNodeIds = outgoingNodeEdges.map((edge) => edge.targetNodeId)
        const nextNodes = nextNodeIds.map((id) => lf.getNodeDataById(id))

        if (nextNodes.length > 0) {
          nextProgressNodes = [...nextProgressNodes, ...nextNodes]
          // console.log(`Node ${node.id} outgoing target nodes:`, nextNodes)
          // return false // 退出循环
        }
      }
    })

    let nextProgressNode = nextProgressNodes.find(
      (item) => item.properties.node_type == 'progress_node'
    )

    // 获取目标节点 ID
    const targetNodeIds = outgoingEdges.map((edge) => edge.targetNodeId)

    // 获取目标节点
    let edgeNextNode = null
    if (targetNodeIds.length > 0) {
      edgeNextNode = lf.getNodeDataById(targetNodeIds[0])
    }

    // 如果 entryNode 不是 progress_node 且下一个节点是 progress_node，则插入新节点
    if (
      entryNode.properties.node_type !== 'progress_node' &&
      edgeNextNode?.properties.node_type == 'progress_node'
    ) {
      addNode = this.insertNodeAfter(
        lf,
        entryNode,
        newNode,
        edgeNextNode,
        level
      )
    } else {
      // 否则，直接在 entryNode 后添加新节点
      addNode = lf.addNode(newNode)
      lf.addEdge({
        sourceNodeId: entryNode.id,
        targetNodeId: addNode.id,
        properties: { level },
      })

      if (nextProgressNodes.length > 0 && nextProgressNode) {
        // 添加从 addNode 到 edgeNextNode 的边，连接到 edgeNextNode 的左侧锚点
        this.lf.addEdge({
          sourceNodeId: addNode.id,
          targetNodeId: nextProgressNode.id,
          sourceAnchorId: `${addNode.id}_outgoing`, // 连接到 addNode 的右侧锚点
          targetAnchorId: `${nextProgressNode.id}_incomming`, // 连接到 edgeNextNode 的左侧锚点
          properties: { level },
        })
      }
    }

    return addNode
  }

  // 封装插入节点函数
  insertNodeAfter(lf, entryNode, newNodeConfig, edgeNextNode, level) {
    const outgoingEdges = lf.getEdgeModels({ sourceNodeId: entryNode.id })
    const addNode = lf.addNode(newNodeConfig)

    outgoingEdges.forEach((edge) => {
      const originalEdgeId = edge.id

      lf.deleteEdge(originalEdgeId)

      // 添加从 entryNode 到 addNode 的边，连接到 addNode 的左侧锚点
      this.lf.addEdge({
        sourceNodeId: entryNode.id,
        targetNodeId: addNode.id,
        sourceAnchorId: `${entryNode.id}_outgoing`, // 连接到 entryNode 的右侧锚点
        targetAnchorId: `${addNode.id}_incomming`, // 连接到 addNode 的左侧锚点
        properties: { level },
      })

      // 添加从 addNode 到 edgeNextNode 的边，连接到 edgeNextNode 的左侧锚点
      this.lf.addEdge({
        sourceNodeId: addNode.id,
        targetNodeId: edgeNextNode.id,
        sourceAnchorId: `${addNode.id}_outgoing`, // 连接到 addNode 的右侧锚点
        targetAnchorId: `${edgeNextNode.id}_incomming`, // 连接到 edgeNextNode 的左侧锚点
        properties: { level },
      })
    })

    return addNode
  }

  /////////////
  //////////////////////////
  ////////////////////////////////////////
}

ContextPad.pluginName = 'contextPad'

export { ContextPad }
