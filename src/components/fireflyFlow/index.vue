<template>
  <div
    :style="{
      position: 'relative',
      height: typeof cHeight == 'number' ? cHeight + 'px' : cHeight,
    }"
  >
    <div
      class="container"
      ref="containerRef"
      :style="{
        width: '100%',
        height: '100%',
      }"
    >
      <div
        class="nodes"
        ref="nodesRef"
        :style="{
          width: '100%',
          height: '99%',
        }"
      ></div>
    </div>

    <InsertMenu
      :lf="lf"
      v-if="showInsertMenu && !userInsertMenuNew2025"
      :left="showInsertMenuX"
      :top="showInsertMenuY"
      :model="showInsertMenuModel"
      :graph="showInsertMenuGraph"
      @is-show="handleHideInsertMenu"
    />
    <InsertMenuNew2025
      :lf="lf"
      v-if="showInsertMenu && userInsertMenuNew2025"
      :left="showInsertMenuX"
      :top="showInsertMenuY"
      :model="showInsertMenuModel"
      :graph="showInsertMenuGraph"
      @is-show="handleHideInsertMenu"
    />
    <el-form
      v-if="drawer && currentModel.type == 'task'"
      :model="currentModel"
      label-width="55px"
      class="demo-form-inline nodes-attr-box"
    >
      <el-row :gutter="10">
        <el-col
          :xs="24"
          :sm="24"
          :md="!containerId ? 24 : 10"
          :lg="!containerId ? 24 : 5"
          :xl="!containerId ? 24 : 5"
        >
          <el-input
            v-model="currentModel.text.value"
            style=""
            :disabled="!containerId ? false : !saveNode ? true : false"
            placeholder="节点名称"
            @change="updateNodeName"
          />
        </el-col>
        <el-col :sm="8" :md="4" :lg="3" :xl="3" v-if="containerId">
          <el-select
            style="width: 100%"
            v-model="currentModel.properties.issue.status_id"
            @change="changeStatus"
            :disabled="!saveNode ? true : false"
          >
            <template #prefix>
              <el-tag
                v-if="
                  typeof currentModel.properties.issue.status_id != 'undefined'
                "
                class="ml-2"
                :type="
                  getStatusClass(currentModel.properties.issue.status_id) ||
                  'primary'
                "
                :style="{ border: 'none' }"
              >
                {{ currentModel.properties.issue.issue_status.name ?? '' }}
              </el-tag>
            </template>
            <el-option
              v-for="(sItem, sKey) in issueStatusList"
              :key="sKey"
              :value="sItem.id"
              :label="' '"
            >
              <el-tag
                class="ml-2"
                :type="getStatusClass(sItem.id) || 'primary'"
                :style="{ border: 'none' }"
              >
                {{ sItem.name }}
              </el-tag>
            </el-option>
          </el-select>
        </el-col>
        <el-col :sm="8" :md="4" :lg="3" :xl="3" v-if="containerId">
          <el-select
            v-model="currentModel.properties.issue.assigned_to_id"
            @change="changedrawerForm"
            style="width: 100%"
            filterable
            placeholder="负责人"
            :disabled="!saveNode ? true : false"
          >
            <el-option
              v-for="(mitem, mkey) in memberList"
              :key="mkey"
              :label="mitem.user.name"
              :value="mitem.user_id"
            />
          </el-select>
        </el-col>
        <el-col
          :xs="24"
          :sm="24"
          :md="16"
          :lg="saveNode || showDetails ? 9 : 10"
          :xl="saveNode || showDetails ? 9 : 10"
          v-if="containerId"
          style="padding: 0px"
        >
          <el-date-picker
            v-model="currentModel.properties.issue.start_date"
            :disabled="!saveNode ? true : false"
            type="date"
            placeholder="开始日期"
            style="width: 130px"
            value-format="YYYY-MM-DD"
            @change="changedrawerForm"
          />
          <span class="text-gray-500">&nbsp;-&nbsp;</span>
          <el-date-picker
            v-model="currentModel.properties.issue.due_date"
            :disabled="!saveNode ? true : false"
            placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 130px"
            @change="changedrawerForm"
          />
          <el-tag type="info" size="large" style="margin-left: 6px">
            {{ currentModel.properties.day_date || '-' }}
          </el-tag>
          天
        </el-col>
        <el-col
          :xs="24"
          :sm="24"
          :md="8"
          :lg="4"
          :xl="4"
          v-if="saveNode || showDetails"
          style="display: flex; justify-content: right"
        >
          <el-upload
            style="margin-right: 5px"
            :headers="headers"
            :action="uploadServer"
            :on-success="handleAvatarSuccess"
            :show-file-list="false"
          >
            <el-button class="btn" :loading="uploading">
              <vab-icon icon="attachments" is-custom-svg class="common-icon" />
              上传附件
            </el-button>
          </el-upload>
          <el-button
            :loading="saveFlowLoading"
            v-if="saveNodeBtn"
            type="primary"
            @click="save"
          >
            保存
          </el-button>
        </el-col>
      </el-row>

      <template
        v-if="containerId && currentModel && currentModel.properties.attachment"
      >
        <div class="file-list-container">
          <FileList
            v-if="currentModel.properties.attachment.length > 0"
            v-model:file-list="currentModel.properties.attachment"
            @on-remove="handleRemove"
            @image-show-status-change="updateEscCauseImg"
            :custom-open-image="true"
            :show-type="'card'"
            @viewer-image="viewerImage"
          />
        </div>
      </template>
    </el-form>

    <!-- ResizableHtml编辑栏 -->
    <div
      v-if="
        drawer &&
        currentModel.type == 'ResizableHtml' &&
        (issueStatusList.length > 0 || !saveNode) // 存在状态列表 或者是 创建模板中修改
      "
      style="pointer-events: none"
    >
      <NodeEditForm
        ref="nodeEditRef"
        :style="{
          position: 'absolute',
          top: getValidPosition(mousePosition.y - 10, 'top') + 'px',
          left: getValidPosition(mousePosition.x - 580, 'left') + 'px',
          // transform: 'translateX(-50%)',
        }"
        :current-model="currentModel"
        :assign-options="assignOptions"
        :container-id="containerId"
        :save-node="saveNode"
        :show-details="showDetails"
        :save-node-btn="saveNodeBtn"
        :issue-status-list="issueStatusList"
        :dialog-form-visible="true"
        @submit-over="nodeSubmitOver"
        @blank-click-event="blankClickEvent"
        @cancel-submit="blankClickEvent()"
      />
    </div>
  </div>
</template>

<script>
  import { ref, onMounted } from 'vue'
  import LogicFlow from '@logicflow/core'
  import {
    MiniMap,
    SelectionSelect,
    Menu,
    DndPanel,
    Highlight,
  } from '@logicflow/extension'
  import NodeRedExtension from './theme/taskTheme'
  import Dagre from './theme/drage'
  // import UserTask from './theme/nodes/UserTask'
  import taskNode from './theme/nodes/taskNode'
  import { ContextPad } from '~/src/components/fireflyFlow/theme/customPlugins/contextPad'
  import { setDndPanel } from '~/src/components/fireflyFlow/theme/customPlugins/setDndPanel'
  import setContextPad from '~/src/components/fireflyFlow/theme/customPlugins/setContextPad'

  import { Control } from '@logicflow/extension'
  // import '@logicflow/core/dist/style/index.css'
  import '@logicflow/core/lib/style/index.css'
  import '@logicflow/extension/lib/style/index.css'
  import {
    getMemberList,
    getEnumerationList,
    getNewIssueStatus,
    deleteAttachment,
  } from '@/api/projectIssue'
  import { doEdit, detail } from '@/api/projectFlow'
  import { issueStatusClass, priorityIcon } from '@/json/issues'
  import { charAt } from '@/utils/common'
  import _, { forEach } from 'lodash'
  import CommonIcon from '~/src/components/CommonIcon.vue'
  import { useUserStore } from '@/store/modules/user'
  import { hasRolePermission } from '~/src/utils/permission'
  import InsertMenu from './tools/InsertMenu.vue'
  import InsertMenuNew2025 from './tools/InsertMenuNew2025.vue'
  import NodeEditForm from './tools/NodeEditForm.vue'

  import { Plus } from '@element-plus/icons-vue'
  import FileList from '~/library/components/FileList/index.vue'
  import {
    flowNodePhase,
    flowNodeCustomLabel1,
    progressNodeCustomFieldPrecondition,
  } from '~/src/json/product'

  export default defineComponent({
    components: {
      CommonIcon,
      InsertMenu,
      InsertMenuNew2025,
      NodeEditForm,
      Plus,
    },
    props: {
      containerId: {
        type: Number,
        default: 0,
      },
      formData: {
        type: Object,
        default: () => {},
      },
      nodes: {
        type: Array,
        default: () => {
          return []
        },
      },
      edges: {
        type: Array,
        default: () => {
          return []
        },
      },
      options: {
        type: Object,
        default: () => {},
      },
      defaultProperties: {
        type: Object,
        default: () => {
          return {
            responsible_user_id: 0,
            status: 3,
            start_date: null,
            complete_date: null,
          }
        },
      },
      defaultNodes: {
        type: Array,
        default: () => {
          return [
            {
              id: 1,
              x: 50,
              y: 100,
              type: 'task',
              text: {
                x: 50,
                y: 100,
                value: '开始',
              },
              properties: {
                issue: {
                  name: '开始',
                },
                node_type: 'progress_node',
                status: 0,
              },
            },
          ]
        },
      },
      // 是否打开控制台
      isControl: {
        type: Boolean,
        default: false,
      },
      // 是否可以保存节点
      saveNode: {
        type: Boolean,
        default: false,
      },
      saveNodeBtn: {
        type: Boolean,
        default: false,
      },
      showDetails: {
        type: Boolean,
        default: false,
      },
      drawer: {
        type: Boolean,
        default: true,
      },
      height: {
        type: [String, Number],
        default: '100%',
      },
      // 是否自动选择节点
      autoSelectNode: {
        type: Boolean,
        default: false,
      },
      productId: {
        type: Number,
        default: null,
      },
      // 来自于何处的调用
      callFrom: {
        type: String,
        default: '',
      },
    },
    emits: ['save', 'fetch-data', 'show-issue-edit'],
    setup(props, { emit }) {
      const $baseMessage = inject('$baseMessage')
      const $baseConfirm = inject('$baseConfirm')
      const $pub = inject('$pub')
      const containerRef = ref(null)
      const route = useRoute()
      const userStore = useUserStore()
      const { user_id, token } = userStore
      const lf = ref()
      const autoDagre = ref()
      const nodeEditRef = ref(null)

      const mousePosition = ref({ x: 0, y: 0 })
      const headers = reactive({
        Authorization: `Bearer ${token}`,
      })

      const state = reactive({
        assignOptions: [], // 项目成员下拉选项
        selectionSelected: false,
        userInsertMenuNew2025: true,
        auth_role: {
          role: ['Admin', 'Manager', 'Product', 'GeneralManager'],
        },
        uploadServer: process.env.VUE_APP_UPLOAD_SERVER,
        btnLoad: false,
        saveFlowLoading: false,
        showInsertMenu: false,
        uploading: false,
        showInsertMenuX: 0,
        showInsertMenuY: 0,
        showInsertMenuModel: null,
        showInsertMenuGraph: null,
        cHeight: props.height,
        context: {
          logicList: [],
          eventCenter: null,
        },
        nodes: Object.assign([], props.nodes),
        edges: Object.assign([], props.egdes),
        currentModel: {},
        drawer: false,
        drawerForm: {
          issue_id: 0,
          status: 3,
          date_day: 0,
          issue: {
            assigned_to_id: null,
            start_date: null,
            due_date: null,
          },
        },
        flowStatusList: [
          {
            value: 3,
            label: '未开始',
          },
          {
            value: 2,
            label: '进行中',
          },
          {
            value: 1,
            label: '已完成',
          },
          {
            value: 4,
            label: '延时',
          },
        ],
        issueStatusList: [],
        priorityList: [],
        doneStatusList: [3, 4, 5, 13], // 完成事项的状态ID列表
        notStatusList: [11], // 未开始事项的状态ID列表
        memberList: [],
        form: _.merge(
          {
            tpl_id: 0,
            flow_type: 'project',
            name: '',
            container_id: props.containerId,
            edges: [],
            nodes: [],
            day_date: '-',
          },
          props.formData
        ),
      })

      const initCanvas = async () => {
        // 获取节点数据
        const graphData = getGraphDataFromContext()
        let width = 0,
          height = 0
        // 检测需要生成画布的大小
        if (graphData.nodes.length > 0 && !props.isControl) {
          let containerWidth = containerRef.value.clientWidth
          let nodeMaxWidth = getNodesMaxWidth(graphData.nodes)
          width = containerWidth > nodeMaxWidth ? containerWidth : nodeMaxWidth
          height = getNodesMaxHeight(graphData.nodes)
          // console.log('1240 height:', height)

          height = height > 600 ? height : 0
        }

        // console.log('1241 height:', height)

        lf.value = new LogicFlow(
          _.merge(
            {
              width: 0,
              height: 0,
              resizable: true,
              container: containerRef.value,
              background: {
                backgroundColor: '#f0f2f5',
              },
              hideAnchors: props.isControl ? false : true, // 是否隐藏锚点
              allowResize: props.isControl, // 是否允许节点resize
              stopZoomGraph: false, // 是否禁止鼠标缩放画布 true=是
              stopScrollGraph: false, // 是否禁止鼠标滚动移动画布 true=是
              stopMoveGraph: false, // 是否禁止移动画布 true=是
              adjustEdge: props.isControl ? true : false, // 禁止调整连线
              adjustEdgeStartAndEnd: props.isControl ? true : false,
              adjustNodePosition: props.isControl ? true : false,
              nodeTextEdit: props.isControl ? true : false, // 禁止修改节点文本
              autoExpand: false, // 节点拖动靠近画布边缘时是否自动扩充画布
              hoverOutline: false,
              edgeSelectedOutline: false,
              plugins: props.isControl
                ? [
                    Control,
                    NodeRedExtension,
                    Menu,
                    DndPanel,
                    SelectionSelect,
                    Highlight,
                    // MiniMap,
                    ContextPad,
                  ]
                : [
                    // MiniMap,
                    NodeRedExtension,
                    Highlight,
                    ContextPad,
                  ],
              pluginsOptions: {
                // miniMap: {
                //   width: 150, // 小地图中画布的宽度
                //   height: 220, // 小地图中画布的高度
                //   showEdge: false, // 是否在小地图的画布中渲染边
                //   headerTitle: '', // 小地图标题栏的文本内容，默认不显示
                //   isShowHeader: false, // 是否显示小地图的标题栏
                //   isShowCloseIcon: false, // 是否显示关闭按钮
                //   leftPosition: 300, // 小地图与画布左边界的左边距
                //   rightPosition: 500, // 小地图与画布右边界的右边距
                //   topPosition: 300, // 小地图与画布上边界的上边距
                //   bottomPosition: 500, // 小地图与画布下边界的下边距
                // },
              },
              keyboard: {
                enabled: true,
                shortcuts: [
                  {
                    keys: 'backspace',
                    callback: () => {
                      const { edges } = lf.value.getSelectElements()
                      // 默认只支持删除选中连线
                      if (edges && edges.length === 1) {
                        lf.value.deleteEdge(edges[0].id)
                      }
                    },
                  },
                ],
              },
              // 网格设置
              grid: {
                size: 8,
                visible: true, // 网格 点 是否可见
                type: 'dot',
                config: {
                  color: '#ffffff',
                  thickness: 1,
                },
              },
              snapGrid: true, // 网格 点 是否吸附，限制最小移动距离
            },
            props.options
          )
        )
        // 启用菜单栏
        setContextPad(lf.value, props.isControl)

        // 节点hover高亮设置
        lf.value.extension.highlight.setMode('path') // hover时高亮当前节点及其path
        lf.value.extension.highlight.setEnable(false) // 设置是否启用高亮

        console.log('lf.value:', lf.value)
        LogicFlow.use(MiniMap)

        // 小地图显示
        lf.value.extension.miniMap?.show() // 不传参默认位置右下角（(left?: number, top?: number)）
        // lf.value.extension.miniMap.setShowEdge(true) // 设置小地图的画布中是否显示边。

        // 画布左侧工具面板
        props.isControl && setDndPanel(lf.value)

        autoDagre.value = new Dagre(lf.value)

        lf.value.setTheme({
          arrow: {
            offset: 4, // 箭头垂线长度
            verticalLength: 2, // 箭头底线长度
          },
        })
        // lf.value.register(Task)
        // lf.value.batchRegister([logicLine, reaction, common, event]);

        // 重新定义右键菜单
        if (props.isControl) {
          lf.value.extension.menu.setMenuConfig({
            nodeMenu: [
              {
                text: '删除',
                callback(node) {
                  $baseConfirm('您确定要删除当前节点吗?', null, async () => {
                    deleteNode(node)
                  })
                },
              },
            ],
          })
        }

        lf.value.render(graphData)

        window.lf = lf.value
        customDragNodeRule()
      }

      // 初始化事件
      const initEvents = () => {
        // 选区监听
        // 选区框选完毕
        lf.value.on('selection:selected', (nodes) => {
          // console.log('selection:selected nodes888888888888888888:', nodes)
          state.selectionSelected = true
        })

        // LF事件注册
        // 点击处理
        lf.value.on('node:select-click', (model) => {
          // this.graph.selectNode(model)
          // state.currentModel = model
        })

        // 属性面板鼠标悬浮到某选项时高亮对应节点
        lf.value.on('node:hover-node', (model) => {
          // this.graph.hoverNode(model)
        })

        // 鼠标移出节点后取消画布组件高亮
        lf.value.on('node:mouseleave', () => {
          // this.context.eventCenter.emit(EDITOR_EVENT.LOGIC_NODE_HOVER, null)
        })

        /**
         * 监听 - 点击node时(选中节点)
         */
        lf.value.on('node:click', ({ data, e }) => {
          if (data) {
            if (state.currentModel && state.currentModel.id != data.id) {
              handleHideInsertMenu()
            }

            handleSelectNode(data)

            // console.log('1231 点击事件:', e)
            // 获取鼠标点击位置
            mousePosition.value = {
              x: e.clientX,
              y: e.clientY,
            }
          } else {
            handleHideInsertMenu()
          }
        })

        // 双击节点时加载属性面板
        lf.value.on('node:dbclick', ({ data, e, position }) => {})

        /**
         * 监听 - 移动节点放开时事件
         */
        lf.value.on('history:change', (model) => {
          // setGraphDataToContext()
          // 可以修改时才自动美化
          // if (props.isControl) {
          //   // 自动美化
          //   autoDagre.value.layout()
          // }
        })

        // 添加节点事件
        lf.value.on('node:add-node', (node) => {
          let y = node.y
          let x = node.x + (node.width > 160 ? node.width + 20 : 160)
          const graphData = lf.value.getGraphData()
          const { edges } = graphData
          edges.forEach((edge) => {
            if (edge.sourceNodeId == node.id) {
              // 计算同级节点最大Y值
              if (edge.endPoint.y >= y) {
                const model = lf.value.getNodeModelById(edge.targetNodeId)
                y = edge.endPoint.y + 50
                x = model.x || edge.endPoint.x
              }
            }
          })

          let newNode = {
            type: 'task',
            x: x,
            y: y,
            text: {
              value: '流程节点',
              x: x,
              y: y,
            },
            properties: {
              status: 0,
              issue: {
                name: '流程节点',
                subject: '流程节点',
                tracker_id: 21, //21为重要事项|流程节点
                status_id: 37, // 未开始
                issue_status: {
                  name: '',
                },
              },
              label: {
                phase: {
                  id: null,
                  name: '',
                  keywords: '',
                  phase_id: 1,
                  color: '#999999',
                  options: flowNodePhase,
                },
                customLabels: [
                  {
                    id: null,
                    name: '',
                    keywords: '',
                    color: '#999999',
                    options: flowNodeCustomLabel1,
                  },
                ],
                customLabelIds: [],
              },
              node_type: 'progress_node',
            },
            level: level,
          }
          if (props.containerId > 0) {
            newNode.properties.issue.project_id = props.containerId
          }
          const addNode = lf.value.addNode(newNode)

          lf.value.addEdge({
            sourceNodeId: node.id,
            targetNodeId: addNode.id,
          })

          // 自动美化
          // autoDagre.value.layout()
        })

        lf.value.on('text:update', (node) => {
          // console.log('text:update node ---:', node)
        })

        lf.value.on('node:delete-node', (model) => {})
        lf.value.on('node:copy-node', (model) => {
          // this.graph.copyNode(model)
        })

        /**
         * 删除节点时
         */
        lf.value.on('node:delete', ({ model, data }) => {
          if (
            data.id &&
            data.properties &&
            typeof data.properties.issue != 'undefined'
          ) {
            if (typeof state.form.delete_node_id == 'undefined') {
              state.form.delete_node_id = []
            }
            state.form.delete_node_id.push(data.id)
          }
        })

        // 点击节点添加按钮后，显示添加菜单
        lf.value.on('node:add-click', ({ model, event }) => {
          state.currentModel = model
          const x = event.clientX
          const y = event.clientY
          this.menuPosition = {
            x: x - 10,
            y: y - 10,
          }
          this.isShowMenu = true
        })

        // 增加边-事件
        lf.value.on('edge:add', (data) => {
          // 之后自动美化
          // autoDagre.value.layout()
        })
        lf.value.on('edge:update-model', (model) => {
          state.currentModel = model
        })
        lf.value.on('node:update-model', (model) => {
          state.currentModel = model
          // this.graph.selectNode(model)
        })
        // 点击边-事件
        lf.value.on('edge:click', (data) => {})

        /**
         * 点击任意空白地方
         * 画布点击事件
         * */
        lf.value.on('blank:click', () => {
          blankClickEvent()
        })
        // 画布拖拽
        lf.value.on('blank:drag', (e) => {
          state.selectionSelected = false
        })

        lf.value.on('blank:contextmenu', ({ e, position }) => {
          // const x = e.clientX
          // const y = e.clientY
          // this.menuPosition = {
          //   x: x - 10,
          //   y: y - 10,
          // }
          // this.isShowMenu = true
        })
      }

      /**
       * 选中第一还没有完成的节点
       */
      const initSelectNode = () => {
        const { nodes } = lf.value.getGraphData()
        if (nodes.length > 0) {
          for (let i in nodes) {
            if (nodes[i].properties.status != 3) {
              lf.value.graphModel.selectNodeById(nodes[i].id, true)
              state.currentModel.type == 'task' && handleSelectNode(nodes[i])
              break
            }
          }
        }
      }

      const initPopover = () => {
        lf.value.graphModel.popover = {
          show: (model, graph, x, y) => {
            state.showInsertMenu = true
            state.showInsertMenuModel = model
            state.showInsertMenuGraph = graph
            state.showInsertMenuX = x
            state.showInsertMenuY = y
          },
        }
        const { popover } = lf.value.extension
      }

      const save = async () => {
        state.saveFlowLoading = true
        await emit('save', lf.value.getGraphData())
        setTimeout(() => {
          setSaveFlowLoading(false)
        }, 2000)
      }

      const setSaveFlowLoading = (value) => {
        state.saveFlowLoading = value
      }

      /**
       * 获取formData中的graphData
       */
      const getGraphDataFromContext = () => {
        let nodes = []
        let edges = []
        if (
          typeof state.form.nodes != 'undefined' &&
          state.form.nodes.length > 0
        ) {
          nodes = Object.assign([], state.form.nodes)
          edges = Object.assign([], state.form.edges || [])
        } else if (
          typeof state.nodes != 'undefined' &&
          state.nodes.length > 0
        ) {
          nodes = Object.assign([], state.nodes)
          edges = Object.assign([], state.edges)
        } else if (
          props.isControl &&
          props.defaultNodes &&
          props.defaultNodes.length > 0
        ) {
          props.defaultNodes.forEach((item) => {
            nodes.push(Object.assign({}, item))
          })
        }
        // 更新节点状态
        nodes.forEach((node) => {
          if (node.properties) {
            node.properties = upProperties(node.properties)
          }
        })

        // 动态决定是否使用新写的插入组件
        if (edges.length > 0 && edges[0].type != 'CustomStyledEdge') {
          state.userInsertMenuNew2025 = false
        }

        return {
          nodes: nodes,
          edges: edges,
        }
        // 结构
      }

      /**
       * 获取最新的数据
       */
      const getGraphData = () => {
        return lf.value?.getGraphData()
      }

      const deleteNode = (node) => {
        lf.value.deleteNode(node.id)
        // emit('save', lf.value.getGraphData())
      }

      /**
       * 更新node节点状态
       * @param {*} node
       */
      const upProperties = (properties) => {
        if (typeof properties.issue != 'undefined') {
          // 更新状态名称
          if (state.issueStatusList.length > 0) {
            for (let i in state.issueStatusList) {
              if (
                state.issueStatusList[i].id == state.drawerForm.issue.status_id
              ) {
                state.drawerForm.issue.issue_status.name =
                  state.issueStatusList[i].name
                break
              }
            }
          }

          // 计算开始时间
          if (properties.issue.start_date) {
            let diffTime = 0,
              effeTime = 0
            if (properties.issue.due_date) {
              const stDate = new Date(properties.issue.start_date)
              const enDate = new Date(properties.issue.due_date)
              const nowDate = new Date()
              // 工作天
              diffTime = Math.abs(enDate - stDate)
              // 超时天
              effeTime = ((enDate - nowDate) / 1000 / 86400).toFixed(0)
            }

            if (effeTime < 0) {
              // 2.1 超时，延时
              properties.status = 4
            } else {
              // 2.2未超时
              if (
                properties.issue.status_id == 1 ||
                properties.issue.status_id == 10
              ) {
                // 2.2.1 未超时未开始
                properties.status = 3
              } else {
                // 2.2.2 未超时已开始
                properties.status = 2
              }
            }
            diffTime = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
            properties.day_date = diffTime > 0 ? diffTime : '-'
          } else {
            // 未开始
            properties.status = 3
          }

          if (state.doneStatusList.indexOf(properties.issue.status_id) > -1) {
            // 1.状态为完成或者关闭时，节点对应也完成
            properties.status = 1
          } else if (
            // 2. 未开始状态
            state.notStatusList.indexOf(properties.issue.status_id) > -1
          ) {
            properties.status = 3
          }
        }
        return properties
      }

      const handleSelectNode = async (node) => {
        updateConfig()
        if (
          !state.drawer ||
          !state.currentModel ||
          state.currentModel.id != node.id
        ) {
          state.drawer = false

          const model = lf.value.getNodeModelById(node.id)
          if (
            typeof node.properties.issue != 'undefined' &&
            props.containerId
          ) {
            state.issueStatusList.length = 0 // 事关响应式刷新-issueStatusList-1
            loadIssueSelect(node)
          }

          // 重新弹出时需要加高度
          if (!state.drawer) {
            const { nodes } = lf.value.getGraphData()
            let maxHeight = 0
            nodes.forEach((item) => {
              if (maxHeight < item.y) maxHeight = item.y
            })
            // 最下面的节点与画布底部距离高于100时不需要再加高度，
            let diff = state.cHeight - maxHeight
          }
          state.currentModel = model
          await new Promise((resolve) => setTimeout(resolve, 50))
          state.drawer = true
        }
      }

      const handleHideInsertMenu = () => {
        if (state.showInsertMenu) {
          state.showInsertMenu = false
        }
      }

      /**
       * 设置graphData数据
       */
      const setGraphDataToContext = () => {
        const graphData = lf.value.getGraphData()
        graphData.nodes.forEach((node) => {
          if (node.properties) {
            delete node.properties.logo
            delete node.properties.status
            delete node.properties.warnings
          }
        })
        state.context.logicList = [graphData]
      }

      /**
       * 设置当前节点的数据到form中以便后续对节点数据的更新
       */
      const setDrawerData = (data) => {
        loadIssueSelect(data)

        if (
          data.properties &&
          (typeof data.properties.length == 'undefined' ||
            data.properties.length > 0)
        ) {
          state.drawerForm = Object.assign({}, data.properties)
        } else {
          state.drawerForm = Object.assign({}, props.defaultProperties)
        }
      }

      /**
       * 加载节点的事项属性选择列表
       * @param data 节点数据
       */
      const loadIssueSelect = (data) => {
        if (props.containerId) {
          // 加载状态列表
          getNewIssueStatus({
            project_id: data.properties.project_id || props.containerId,
            tracker_id: data.properties.issue.tracker_id || 21,
            status_id: data.properties.issue.status_id,
            author_id: data.properties.issue?.author_id,
            assigned_to_id: data.properties.issue?.author_id,
          }).then(async (res) => {
            await new Promise((resolve) => setTimeout(resolve, 50)) // 事关响应式刷新-issueStatusList-2
            state.issueStatusList = res.data
            changeStatus(data.properties.issue.status_id)
          })
          // 加载成员列表
          getMemberList({
            filter: {
              project_id: props.containerId,
              user: { type: 'User' },
            },
            limit: 100,
          }).then((res) => {
            if (typeof res.data.data != 'undefined') {
              state.memberList = res.data.data
            }
          })
        }
      }

      const getStatusClass = (v) => {
        return issueStatusClass[v] || ''
      }

      const getNodesMaxWidth = (nodes) => {
        let maxWidth = 0
        nodes.forEach((node) => {
          if (node.x > maxWidth) {
            maxWidth = node.x
          }
        })
        return maxWidth > 0 ? maxWidth + 100 : 0
      }

      const getNodesMaxHeight = (nodes) => {
        let maxHeight = 0
        nodes.forEach((node) => {
          if (node.y > maxHeight) {
            maxHeight = node.y
          }
        })
        return maxHeight > 0 ? maxHeight + 50 : 0
      }

      const getNodesMinY = (nodes) => {
        let MinY = 0
        nodes.forEach((node) => {
          if (node.y < MinY) {
            MinY = node.y
          }
        })
        return MinY
      }

      const getNodesMaxX = (nodes) => {
        let MaxX = 0
        nodes.forEach((node) => {
          if (node.x > MaxX) {
            MaxX = node.y
          }
        })
        return MaxX
      }

      const IssueStatusToNodeStatus = (status) => {
        let statusList = {
          11: 3,
          12: 2,
          13: 1,
        }
        return statusList[status] || 3
      }

      /**
       * 查看事项详情
       */
      const showIssueEdit = (row = null) => {
        if (!row) {
          if (state.currentModel && state.currentModel.properties.issue_id) {
            row = { id: state.currentModel.properties.issue_id }
          } else {
            return
          }
        }
        emit('show-issue-edit', row)
      }

      const handleClose = (done) => {
        done()
      }

      // 更新节点名称时同步更新properties里的name值
      const updateNodeName = () => {
        state.currentModel.properties.issue.name = state.currentModel.text.value
        state.currentModel.properties.issue.subject =
          state.currentModel.text.value
      }

      const changedrawerForm = () => {
        if (typeof state.currentModel.properties.issue != 'undefined') {
          let properties = upProperties(state.currentModel.properties)
          // state.currentModel.properties = properties
        }
        // save()
      }

      // 更新状态事件
      const changeStatus = (v) => {
        for (let i in state.issueStatusList) {
          if (state.issueStatusList[i].id == v) {
            state.currentModel.properties.status = IssueStatusToNodeStatus(
              state.issueStatusList[i].id
            )
            if (!state.currentModel.properties.issue.issue_status) {
              state.currentModel.properties.issue.issue_status = {
                name: state.issueStatusList[i].name,
              }
            } else {
              state.currentModel.properties.issue.issue_status.name =
                state.issueStatusList[i].name
            }

            // 重新计算节点状态
            upProperties(state.currentModel.properties)
            return
          }
        }
        changedrawerForm()
      }

      const handleUpdateGraph = () => {
        state.form = Object.assign({}, props.formData)
        const graphData = getGraphDataFromContext()
        lf.value.render(graphData)
      }

      /**
       * 根据isControl，更新画布配置
       */
      const updateConfig = () => {
        lf.value.updateEditConfig({
          adjustEdge: props.isControl ? true : false, // 禁止调整连线
          adjustEdgeStartAndEnd: props.isControl ? true : false,
          adjustNodePosition: props.isControl ? true : false,
          nodeTextEdit: props.isControl ? true : false, // 禁止修改节点文本
        })
      }

      /**
       * 用户是否产品成员
       */
      const isMember = () => {
        return state.memberList.indexOf(user_id) > -1 ? true : false
      }

      /**
       * 处理上传成功
       */
      const handleAvatarSuccess = async (res) => {
        if (state.currentModel) {
          if (
            typeof state.currentModel.properties.attachment == 'undefined' ||
            !state.currentModel.properties.attachment
          ) {
            state.currentModel.properties.attachment = []
          }
          let list = [
            ...state.currentModel.properties.attachment,
            {
              filename: res.data.upload.attachment.filename,
              url: res.data.upload.attachment.url,
              id: res.data.upload.attachment.id,
            },
          ]
          state.currentModel.properties.attachment = []
          state.currentModel.properties.attachment = list
        }
      }

      const handleRemove = (file) => {
        deleteAttachment({ id: file.id }).then(() => {
          const index = state.currentModel.properties.attachment.findIndex(
            (item) => item.id === file.id
          )
          let list = [...state.currentModel.properties.attachment]
          list.splice(index, 1)
          emit('save', lf.value.getGraphData())
          state.currentModel.properties.attachment = []
          state.currentModel.properties.attachment = list
          // $baseMessage(`删除成功`, 'success', 'vab-hey-message-error')
        })
      }

      const updateEscCauseImg = (v) => {}

      const viewerImage = (row) => {
        $pub('viewer-image', row)
      }

      /**
       * 自定义增加taskNode移动拖动规则，迭代获取子级一并移动
       */
      const customDragNodeRule = () => {
        lf.value.graphModel.addNodeMoveRules((model, deltaX, deltaY) => {
          // 如果移动的是taskNode，那么taskNode的子节点也跟着移动。
          // 框选时，不使用自定义的移动规则。
          let dragNodeData = lf.value.getNodeDataById(model.id)
          if (
            (dragNodeData.type != 'task' &&
              dragNodeData.type != 'ResizableHtml') ||
            state.selectionSelected
          ) {
            return true
          }
          let dragNodeChildren = lf.value.getNodeOutgoingNode(model.id)
          let stack = []
          _.forEach(dragNodeChildren, (child) => {
            if (child.properties.node_type == 'task_node') {
              stack.push(child.id) // 将未处理的子节点加入栈中
            }
          })
          let childrenIds = []
          // 遍历栈，获取所有子节点
          while (stack.length > 0) {
            // 弹出栈顶节点
            let currentNodeId = stack.pop()
            // 如果当前节点已处理过，则跳过
            if (childrenIds.includes(currentNodeId)) {
              continue
            }
            // 将当前节点 ID 加入已处理列表
            childrenIds.push(currentNodeId)
            // 获取当前节点的子节点
            let moreChildren = lf.value.getNodeOutgoingNode(currentNodeId)
            moreChildren.forEach((child) => {
              if (!childrenIds.includes(child.id)) {
                if (child.properties.node_type == 'task_node') {
                  stack.push(child.id) // 将未处理的子节点加入栈中
                }
              }
            })
          }
          lf.value.graphModel.moveNodes(childrenIds, deltaX, deltaY, true)
          return true
        })
      }

      const blankClickEvent = () => {
        console.log('blankClickEvent:')
        if (state.currentModel) {
          setTimeout(() => {
            // autoDagre.value.layout()
            state.currentModel = null
            state.drawer = false
            handleHideInsertMenu()
          }, 150)
        }
        state.selectionSelected = false
      }

      // 节点编辑完成
      const nodeSubmitOver = (syncFlowSave = false) => {
        // blankClickEvent()
        if (syncFlowSave && props.callFrom == 'product_detail') {
          emit('save', lf.value.getGraphData())
        } else {
          blankClickEvent()
        }
      }

      /**
       * 节点编辑完成
       */
      const nodeEditFormInitSaveFunc = (syncFlowSave = false) => {
        nodeEditRef.value.cancelSubmit(syncFlowSave)
      }

      /**
       * 获取成员列表
       */
      const handleMemberList = async (projectIdIn) => {
        let projectId = projectIdIn ?? props.productId
        if (route.query.product_id) {
          projectId = parseInt(route.query.product_id)
        }
        if (!projectId) return

        let memberqueryForm = {
          limit: 1000,
          filter: {
            project_id: projectId,
            user: { type: 'User', status: 1 },
          },
          op: { user: { status: '=' } },
        }
        const {
          data: { data },
        } = await getMemberList(memberqueryForm).catch((r) => {
          return { data: '' }
        })

        state.assignOptions = data
        // console.log('1231state.assignOptions:', state.assignOptions)
      }

      const getValidPosition = (value, direction) => {
        const padding = 10 // 预留一些边距
        const containerWidth = window.innerWidth // 也可以用 ref 绑定外层容器
        const containerHeight = window.innerHeight

        if (direction === 'left') {
          return Math.min(Math.max(padding, value), containerWidth - 600) // 假设弹窗宽度 600px
        }
        if (direction === 'top') {
          return Math.min(Math.max(padding, value), containerHeight - 620)
        }
        return value
      }

      onMounted(async () => {
        // console.log('11111111111:22222222222222', props.nodes)
        // console.log('props.formData', props.formData)
        // console.log('props.productId', props.productId)
        handleMemberList()
        await new Promise((resolve) => setTimeout(resolve, 200))
        // console.log(containerRef.value)
        // LogicFlow.use(Control)
        await initCanvas()
        initEvents()
        initPopover()
        initSelectNode()
      })
      return {
        ...toRefs(state),
        lf,
        containerRef,
        handleClose,
        changedrawerForm,
        priorityIcon,
        getStatusClass,
        save,
        getGraphDataFromContext,
        getGraphData,
        updateNodeName,
        changeStatus,
        handleUpdateGraph,
        showIssueEdit,
        isMember,
        hasRolePermission,
        handleHideInsertMenu,
        headers,
        handleAvatarSuccess,
        handleRemove,
        updateEscCauseImg,
        viewerImage,
        setSaveFlowLoading,
        customDragNodeRule,
        blankClickEvent,
        nodeSubmitOver,
        mousePosition,
        getValidPosition,
        nodeEditRef,
        nodeEditFormInitSaveFunc,
      }
    },
  })
</script>

<style lang="scss" scoped>
  :deep() {
    .lf-graph {
      height: 99%;
    }
    .lf-control-save {
      background: url(data:image/png;base64,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);
      background-size: cover;
    }
    .form-item-label {
      .el-form-item__label {
        width: 42px !important;
      }
      label {
        width: 42px !important;
      }
    }
    .el-icon.avatar-uploader-icon {
    }
    .el-upload.el-upload--picture-card {
      width: 50px;
      height: 50px;
    }
  }
  .container {
    height: 500px;
  }
  .container:first-child {
    overflow: auto !important;
  }
  .logicflow-custom-panel {
    position: absolute;
    top: 0;
    z-index: 1;
    display: flex;
    align-items: center;
    height: 50px;
    padding: 5px 10px;
    background: #ffffff;
    border: 1px solid #efeaef;
    box-shadow: 0 1px 4px rgb(0 0 0 / 30%);
  }
  svg {
    margin: 0px 5px;
  }
  svg:first-child {
    margin-left: 0px;
  }
  svg:last-child {
    margin-right: 0px;
  }

  .demo-form-inline .el-input {
    --el-input-width: 220px;
  }
  .nodes-attr-box {
    position: absolute;
    bottom: 20px;
    left: 50%;
    width: calc(100% - 40px);
    padding: 20px 12px;
    padding-bottom: 10px;
    background-color: #ffffff;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    transform: translate(-50%);
  }
  .el-col {
    margin-bottom: 10px;
  }
  .flow-save-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    span {
      color: #666666;
    }
    i {
      color: #666666;
    }
  }

  .el-icon.avatar-uploader-icon {
    width: 50px;
    height: 50px;
    text-align: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
  }
  .file-list-container {
    max-height: 90px;
    overflow-y: auto;
  }
</style>

resizableHtml.js专用class
<style>
  .lf-context-item {
    width: 24px;
    height: 24px;
    padding: 2px 2px;
  }
  .node-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    background-color: #f9f9f9;
    border: 2px solid #007bff;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .node-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    padding: 10px;
  }

  .node-header {
    display: flex;
    gap: 8px;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }

  .node-status {
    width: 8px;
    min-width: 8px;
    height: 8px;
    background-color: #28a745;
    border-radius: 50%;
  }

  .node-footer {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .node-avatar {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-color: #ccc; /* 默认头像颜色填充，可替换为图片 */
    border-radius: 50%;
  }

  .node-date {
    flex: 1;
    /* font-family: "PingFang SC"; */
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #999;
    text-align: left;
    word-break: break-all;
  }
</style>
