.DS_Store
node_modules
node_modules.nosync
/dist
.env.local
library/build/vuePlugins/components.d.ts

# local env files
.env.local
.env.*.local

# Log files
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Lock files
yarn.lock
pnpm-lock.yaml

# Yarn v2 not using using Zero-Installs
.yarn/*
#!.yarn/cache
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# Vab
public/video
*.zip
*.7z
*.rar
/library/styles/variables/vab-red-variables.module.scss
/.history
/dist-online
library/build/vuePlugins/components.d.ts
library/build/vuePlugins/auto-imports.d.ts


# ai generated
ai_issues*
ai_summary*
.kilocode
.lingma
doc/